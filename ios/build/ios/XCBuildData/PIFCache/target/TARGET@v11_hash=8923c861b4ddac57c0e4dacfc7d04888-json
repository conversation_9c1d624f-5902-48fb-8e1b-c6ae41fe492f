{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98d014f9d4243b075b77c5a8be8abb0d07", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHONE=1", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989fd7c711885b3327f23706f1d8120e6d", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e1670c1185f324366d5fa738e8d5c730", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHONE=1", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98964aa4eab82c99c03994299cfa9bf1d5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e1670c1185f324366d5fa738e8d5c730", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHONE=1", "INFOPLIST_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseInstallations/FirebaseInstallations.modulemap", "PRODUCT_MODULE_NAME": "FirebaseInstallations", "PRODUCT_NAME": "FirebaseInstallations", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98757608326ef57f7c7354b7b198003a58", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fe612c3118da86024128377bc45a00f9", "guid": "bfdfe7dc352907fc980b868725387e98520bc96f321cf007bbb8c3255fd66bd2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98904a78981ff973d567b8940dfaab08e4", "guid": "bfdfe7dc352907fc980b868725387e981701f9ecdfb5ef656e5afcc24f49bcfd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986828a2cca3361ce417eae98bdd5ae444", "guid": "bfdfe7dc352907fc980b868725387e98ff87fa6e4db25b9b5f7b89d9dec1c3c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892b256a8f953dd31d2d68ec3e803e22d", "guid": "bfdfe7dc352907fc980b868725387e98fc0472f0e85f11fb6854a04e7a12fa74"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814afc568c6caccf5c7dbfc44c1c98007", "guid": "bfdfe7dc352907fc980b868725387e980dd89450d89e5ea06147801a2dd2389a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f1d3c7d12b1a9bc07a0bc74d6d5e944", "guid": "bfdfe7dc352907fc980b868725387e98ea5fc14eb658f50583507a3acca0be5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983393bae345ccac60fc58b04664167a1f", "guid": "bfdfe7dc352907fc980b868725387e98064a20c0c03c7b804d72b9c3b0c8d95b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866d9b1762534d3d82cc6b2d16ee8e896", "guid": "bfdfe7dc352907fc980b868725387e98caa16a2d892eaa2cc269ed4ff18b02eb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf8d9fddcb87c42887d5453f03f1a0eb", "guid": "bfdfe7dc352907fc980b868725387e9833903e09c0619818a10bb94bf282157c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a063323172c1ffa47d77c0afcc22951", "guid": "bfdfe7dc352907fc980b868725387e989f676586b35a63ac22c665945eaf16b9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c4a69c4b30c24250e4460925325cea93", "guid": "bfdfe7dc352907fc980b868725387e981c6e45f939c1ffb0811123bcf12dc4b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4010d7720c6574c101b3446c3101a2d", "guid": "bfdfe7dc352907fc980b868725387e98ea43bde0cb221bc14731cd04f6495937"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982e47288e85d7b360dd251dfd985d84a0", "guid": "bfdfe7dc352907fc980b868725387e987b24107c8f13b905bde004f3ad474a87", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982051495d4e96475178a221c29677f239", "guid": "bfdfe7dc352907fc980b868725387e98da591344920d3633258698704abd5bca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98656cda0123a6b0a9da1766d75b66725b", "guid": "bfdfe7dc352907fc980b868725387e981ef8c42a9dd472599212dbcba026bb56"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9896f914b8a41ab20d369c4db6642b678b", "guid": "bfdfe7dc352907fc980b868725387e98226ed00344d631dac24317e2b9200d9e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d9b60431e9213eec3d8448f2a3e54ad", "guid": "bfdfe7dc352907fc980b868725387e9801863119b61bc8d7f94d668894930ce6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cb39333873750225daa5c00aa78c3204", "guid": "bfdfe7dc352907fc980b868725387e9874dd229ed4c7e5509d44d3fd0c320667"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980248b6677bea0da1bb489f354833922d", "guid": "bfdfe7dc352907fc980b868725387e98368dedae110b8d30cd7c8b2ac1b19cbf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98632fdd2af588a98a4d178ee126debba7", "guid": "bfdfe7dc352907fc980b868725387e9858787d8869c91ae109972cb29af3ba13"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98587ced4bd0e4afa21118c985432b15dd", "guid": "bfdfe7dc352907fc980b868725387e98f57f3d2c2c5f60a6fa5621fd7ccc48d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f1675ab69daf32d01180b9ec0a31ea6", "guid": "bfdfe7dc352907fc980b868725387e98b8000b66bd34067a48ea6a4eb54674d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9611639e0b7daee76324ba5816c8634", "guid": "bfdfe7dc352907fc980b868725387e98f13ffd64168012298ba0f4ea32c880e6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbf64f7d584365cea36fbe2ae908e21a", "guid": "bfdfe7dc352907fc980b868725387e983ab3f6ad67feac332844282956deb41b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6b0fb5ceddcd77190579311c702f511", "guid": "bfdfe7dc352907fc980b868725387e98c5af87555813bdd892be7c5043bd9986"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9885b8ac767fe0a5f9c5f7a7dd11d3c669", "guid": "bfdfe7dc352907fc980b868725387e98301d1f048a57b49b6efe332ce04b7aed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851d715b9eaa8f8d2afeb1a966a5a94e9", "guid": "bfdfe7dc352907fc980b868725387e98ec0c8d7203b6b0823ad786c6a1546f16"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985af9cbdd2cce9ee8f8a8cb8de1c9bd4a", "guid": "bfdfe7dc352907fc980b868725387e98eb32c6543481c624be6baf5a534a71cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ad6def976a371779bae12459e400a88", "guid": "bfdfe7dc352907fc980b868725387e98b119784433b141abdde62825141d66ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983092d609df2cea2cd391bd5a9162322a", "guid": "bfdfe7dc352907fc980b868725387e98facd4191ee622fbfcddfe511f134ca93"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bf933bceb132f9ad81bd3803bab8af78", "guid": "bfdfe7dc352907fc980b868725387e98b4aa4f2bfc71b2551591810396f43388"}], "guid": "bfdfe7dc352907fc980b868725387e9861a5d52a66b906ec3662ee23b2e9e5b6", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987719cbe14d337508b054181aec0bf6e5", "guid": "bfdfe7dc352907fc980b868725387e98f59e25d1e47a992239c1db942621b186"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fef2a7fa220672949ae40a5e05ff668", "guid": "bfdfe7dc352907fc980b868725387e983b2bd0472f1f55f74dcbf2ddfbc7ac8d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cde01488aaa1c6ea93cc5a896b9f847", "guid": "bfdfe7dc352907fc980b868725387e98152225c3e60615d5c472272a85b0ff6b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c21f098515dd486424852c09e4ca7d34", "guid": "bfdfe7dc352907fc980b868725387e9895dd094b58be71d19d7b85833ef470c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5a967de272bfa0994931e3cd6f98f0e", "guid": "bfdfe7dc352907fc980b868725387e98d9635b9b722db23dc6ae19bd874d773c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849eb5e5380ae3aaec1b2e7aa861aca4e", "guid": "bfdfe7dc352907fc980b868725387e98521dbaf2092793a965c823cab72cbae7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986499231cac8f847b51e7fa55cf4b7ff9", "guid": "bfdfe7dc352907fc980b868725387e98b36848e317e4cb6b5f21f177f805dff2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984af0b7d31baeb590bad4e1151f42f0a6", "guid": "bfdfe7dc352907fc980b868725387e98f3b60f8ab95d857372c6b2f2640f8d03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b89792cbd4c8263f79edc13659b01c73", "guid": "bfdfe7dc352907fc980b868725387e98178dee725714fffe263172af13ec6542"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98755a351f43f6a78dfe06a0d9ff6e30b3", "guid": "bfdfe7dc352907fc980b868725387e98ee151d6912522f52a025f531d4cc9b5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da1d32408453a789ccada37e652190cc", "guid": "bfdfe7dc352907fc980b868725387e983aeab08ef87b1113584120cdc8c4e789"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98457bdc182af9e0235481d5c44e4808c2", "guid": "bfdfe7dc352907fc980b868725387e9863b0d95d5e3ad5b63e812ff0e6e11499"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c2128a180850ce77dd34b6dddb4ce35", "guid": "bfdfe7dc352907fc980b868725387e981c5996a1aa0c96321c54f27be68b487d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d0560aeb6f8e68b98cad60324ddc4bc2", "guid": "bfdfe7dc352907fc980b868725387e9826e513455fe1791f2dfd584da2b9a175"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989ffc34bbba90ae674a7455b54b56b781", "guid": "bfdfe7dc352907fc980b868725387e98b8b3c8c82a4f9ff20248d7ff0c45a9e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980f122455cf268f002ad56507ece580bf", "guid": "bfdfe7dc352907fc980b868725387e98e0cfb1721bfd766cc8749d5bf7c6bc53"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9ca20530a3264e7f6c69d2965b1c868", "guid": "bfdfe7dc352907fc980b868725387e983bde3f54e0089773c33e577e165f2cb6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf9e57c921a2fe6bcecbba03c431c28b", "guid": "bfdfe7dc352907fc980b868725387e980a51e6914b40db3b1f4523b48b5e53f0"}], "guid": "bfdfe7dc352907fc980b868725387e98a7c0b54e76c7f6ac9dec2c4c7f2dd4c4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e986be341ff9de2843c47219f70a7656771"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857517583cea0339c53d5dd4c2679b551", "guid": "bfdfe7dc352907fc980b868725387e98a9eb6370a63a33a46cbed155e3971607"}], "guid": "bfdfe7dc352907fc980b868725387e98b7b58551324b12e183b60603be2ed975", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98711d7c4a9fa4d57db3b418bde579d4a5", "targetReference": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc"}], "guid": "bfdfe7dc352907fc980b868725387e983dbf6e0498029e062de2dacd8823c895", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e984535f130e81fa6507008242e4e8916fc", "name": "FirebaseInstallations-FirebaseInstallations_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}, {"guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC"}], "guid": "bfdfe7dc352907fc980b868725387e98566ec9a1d71c4629f4f85ecb735ce614", "name": "FirebaseInstallations", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9860819b8e327bf41b291e92315614a812", "name": "FirebaseInstallations.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}