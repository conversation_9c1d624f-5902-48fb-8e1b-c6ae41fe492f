{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987589fcaadc8ea2bebe5cc3a47dc813c0", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/firebase_auth/firebase_auth-prefix.pch", "INFOPLIST_FILE": "Target Support Files/firebase_auth/firebase_auth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/firebase_auth/firebase_auth.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "firebase_auth", "PRODUCT_NAME": "firebase_auth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98fc2e72882bf77e3e5ac0ef2803689502", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987a12622a4545544aa2b90adf36ea4da5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/firebase_auth/firebase_auth-prefix.pch", "INFOPLIST_FILE": "Target Support Files/firebase_auth/firebase_auth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/firebase_auth/firebase_auth.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "firebase_auth", "PRODUCT_NAME": "firebase_auth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ae039069321fc7f3cc1ac77369d0d961", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e987a12622a4545544aa2b90adf36ea4da5", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/firebase_auth/firebase_auth-prefix.pch", "INFOPLIST_FILE": "Target Support Files/firebase_auth/firebase_auth-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/firebase_auth/firebase_auth.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "firebase_auth", "PRODUCT_NAME": "firebase_auth", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985d4ea330ac5e66f124254361aac78feb", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98dbb89f9567397a7a8676cf75974642c6", "guid": "bfdfe7dc352907fc980b868725387e98a6f7ce8b620209e395aa879820fb44c5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b337648201b37cc06e472521b6202222", "guid": "bfdfe7dc352907fc980b868725387e981c9ad3f8085deaca9ef2f6d26997ab17", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989559b9d78b45217254d9e02f21cd90bf", "guid": "bfdfe7dc352907fc980b868725387e98c47501d102d31805b5e2534266595315", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ac543405583c30a65fa4bbef5aca911", "guid": "bfdfe7dc352907fc980b868725387e983a56c057b863db7a6a6fc831038f8129", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f025ac52a7fa992718f6e25c61de0d49", "guid": "bfdfe7dc352907fc980b868725387e98a56e2b1c3cf2248dd135639da23e2693", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f334a57f5cafe1313b68e8cc0ffb0552", "guid": "bfdfe7dc352907fc980b868725387e98e07c96530e0e40ebeab7fe74b5a73ad6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cbd30ebff1c16d455b442783b2c2caf", "guid": "bfdfe7dc352907fc980b868725387e98905cee5dab88473b1856a1e3873e029b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98541434c0624f5dc545f0cabaa22cf0e8", "guid": "bfdfe7dc352907fc980b868725387e98d2387220de5e4c530b088b631985afdf", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983dc6945c09512dc280ff477e6a055486", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e984ada517aaae055ee4e607907af4b1457", "guid": "bfdfe7dc352907fc980b868725387e982e4c5611f663e94b361b05075d3011cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984eb46dba3cceeeb5806dd1bb7f8084a8", "guid": "bfdfe7dc352907fc980b868725387e98ad11deacfe9774449f43fe9d59f69393"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d89910b43caad75fecb701e6e0c3aba4", "guid": "bfdfe7dc352907fc980b868725387e9862c02dda001b6855b10137a50c9befb8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d20b4c83de362f2bb56f0dda064de2ee", "guid": "bfdfe7dc352907fc980b868725387e987c077340da40e4e62317f0a381aec27f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989d040cf81802a42ee75430195403994e", "guid": "bfdfe7dc352907fc980b868725387e98848128b0939059925b3241d02cbee94e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989452ad00e32bfd3897dcba29cb7d2a19", "guid": "bfdfe7dc352907fc980b868725387e989ea0e7f8f06bb6e6278d52c21fa699f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db9f7d2cea15a973f26c84fff9bbf8c5", "guid": "bfdfe7dc352907fc980b868725387e981f2f13dcbe5051451976c9827fb784d4"}], "guid": "bfdfe7dc352907fc980b868725387e988609ddafb90352c23b89a6c96ed9b16f", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e98e9b83fccdb550a9ba519f430dc704c93"}], "guid": "bfdfe7dc352907fc980b868725387e98ddfc4316b232572929d9ca579ec0c94e", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98117c12e2cce513e0ed72e47ea361ea89", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e983788d8769c821650606514be955fca93", "name": "firebase_auth", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e984496a3f7661d89567ff8250961054e8f", "name": "firebase_auth.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}