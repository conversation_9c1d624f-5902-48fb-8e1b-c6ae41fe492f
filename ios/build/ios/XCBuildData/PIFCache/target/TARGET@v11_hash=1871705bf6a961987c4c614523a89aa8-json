{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cb2d2adb27b7c61d28a1efa9d2486d2c", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981d92f5a40c85694a2825091a535266ab", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fa995e6511853a1533f402dad2c86189", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9822b7961eea04e3e4d720e6c0eb72e523", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fa995e6511853a1533f402dad2c86189", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "10.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GTMSessionFetcher/GTMSessionFetcher.modulemap", "PRODUCT_MODULE_NAME": "GTMSessionFetcher", "PRODUCT_NAME": "GTMSessionFetcher", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d132364654a501ac32b67673380d25bf", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9868900afa44ad64edaad6012ff27f3e3a", "guid": "bfdfe7dc352907fc980b868725387e98ccaf0ac2bf1ddbd17048c8167b48181e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9894a3c38a641c54448d6709b1ea95844b", "guid": "bfdfe7dc352907fc980b868725387e98ea86d0721600e43001962400e4cf3723", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98008697cac84579f954f39fc7e1a6d4ee", "guid": "bfdfe7dc352907fc980b868725387e983871c9f272cfa3a13520de8884f6d317", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9805176687a695059538635873a6818463", "guid": "bfdfe7dc352907fc980b868725387e989e12ad31c4764a570f332384fc1393d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bb622fa27b77164dd212a1c1351b0bb8", "guid": "bfdfe7dc352907fc980b868725387e98c2b4ed492a43fe86b5872e56c56830b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98889fcbdb1362ce90bdb57ae65d4d6df8", "guid": "bfdfe7dc352907fc980b868725387e982b74960cb07a718df833f2c93a0f0585", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9822d6fd0ff2e243e99e3dfd2634793f40", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981c92c4dcff291338fc0e6fe3191729f0", "guid": "bfdfe7dc352907fc980b868725387e9897b3e2fba2daf940296c50a179972a68"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2fbe89806a275cb15b3d4d4735567c7", "guid": "bfdfe7dc352907fc980b868725387e9832184b6998f8abb16b9957cc7ad355ea"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884fa520a693396ca42f6ec6a9791cdba", "guid": "bfdfe7dc352907fc980b868725387e98a1c03847aa2814c24c9471ede74f9f8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e73d2df62f72bf3e46bc569977ed0c5e", "guid": "bfdfe7dc352907fc980b868725387e9848bf76b3dc643a5c407cafea5f67b0fa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe8d6d90bdc761921b4b9ae822474069", "guid": "bfdfe7dc352907fc980b868725387e98861354665336fdd572a62fb150faccfa"}], "guid": "bfdfe7dc352907fc980b868725387e98f7671566b0941d8d90a953d5da325081", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e98ce544487b515597cd5192aca7646feda"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857517583cea0339c53d5dd4c2679b551", "guid": "bfdfe7dc352907fc980b868725387e988ef3ab89f695a25c4ffb029ae0b62c44"}], "guid": "bfdfe7dc352907fc980b868725387e98c529152f46c09ec750c2e9868715b620", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98744c376157b90292db7326c350225d58", "targetReference": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1"}], "guid": "bfdfe7dc352907fc980b868725387e989a4c18dded5444b750104f2cf15c3b84", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e9801af34ddea6be97d757786022edb34b1", "name": "GTMSessionFetcher-GTMSessionFetcher_Core_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f65e88472d384b1ba0888326befb3a8e", "name": "GTMSessionFetcher.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}