{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98e0253b7c3d693a2be0e0723c534423c2", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHONE=1", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98808d84e7ddeac2ee6fb77adcf1ff9792", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c1ac77c43cdfe52611af4fc25ff29fc3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHONE=1", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9822e8ab6070592494896a37d6373860c3", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98c1ac77c43cdfe52611af4fc25ff29fc3", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHONE=1", "INFOPLIST_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseStorage/FirebaseStorage.modulemap", "PRODUCT_MODULE_NAME": "FirebaseStorage", "PRODUCT_NAME": "FirebaseStorage", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e057a79b2d055675fa9f403ef2bebffd", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b663ad0c84fecfa1f3456ef94c62af25", "guid": "bfdfe7dc352907fc980b868725387e984b72693c928268fc66d6f202815aae1f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879a739ef8d488403e9d64da1c9ddeb24", "guid": "bfdfe7dc352907fc980b868725387e983a601339c360262a88a45a8df4c622c6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98620760e81f8e908cf15eb69f09c22c04", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982d0ab3172ed974e42ee1741869c34610", "guid": "bfdfe7dc352907fc980b868725387e98a39a3811bc38c135e2fc4621c7b76c11"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840489a866c1ab3edc89316b3fa5111a2", "guid": "bfdfe7dc352907fc980b868725387e98903eb1e5b3572f1a06524c7a49ea1a41"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9895fc692fdb0a2344c86bb12e8df35f86", "guid": "bfdfe7dc352907fc980b868725387e980c1f84ce0cabaf770888252b24f947e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a86dec73106240cd82385cadacc2e60b", "guid": "bfdfe7dc352907fc980b868725387e98744a2763c0b13027c5001bc2324558e9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c59fc7507c595f8030703252a1b90c6c", "guid": "bfdfe7dc352907fc980b868725387e98008e83a6d77211ef670befdcf287d831"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9897ad642ec4ae8f1d63e468c93a8c5339", "guid": "bfdfe7dc352907fc980b868725387e9803491d4a3dd35c2a90b8c33b8a52e711"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edba1dcb22a258d7f9a466ab674d4d14", "guid": "bfdfe7dc352907fc980b868725387e983d61909b250d222809a9fab37485b789"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d0490fbbc8e9e1d6cac79d238de773b", "guid": "bfdfe7dc352907fc980b868725387e9873879942480874f17b998e13ce328494"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98889819028b919759d6d217ed101b5c87", "guid": "bfdfe7dc352907fc980b868725387e98436dd9e448cf7686f285e0ea474e0466"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868455300d01b51acfdd852aaac8ed62a", "guid": "bfdfe7dc352907fc980b868725387e986d4b810a0a589d1f2ab325319c6814b5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98212da9410473e43d888986bd502373ce", "guid": "bfdfe7dc352907fc980b868725387e98f9685c8e1fd8baff2514403173e8768a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e01803ca1d83a5c9e4922707c1000511", "guid": "bfdfe7dc352907fc980b868725387e98c526f40f50fb67d7bba9379743af4255"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9817ebbd4ec0440b9ef2434f0f667ec19b", "guid": "bfdfe7dc352907fc980b868725387e989627a23bd1c0068edf64e6e656ae5c38"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857dee7e7355459a3a7e1762dc97e019b", "guid": "bfdfe7dc352907fc980b868725387e98acf48ca664dd8581e032ef60a32e4101"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884dca81e749015da688b9d6f411299e9", "guid": "bfdfe7dc352907fc980b868725387e98df1a456d48250b45ac46f791bca88eec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9835f380541648ea66700adc5049e659d5", "guid": "bfdfe7dc352907fc980b868725387e98a8a83ea81b1aa781724ce7ef889b88a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827d02f2abdeb703c63ff4352e92eec04", "guid": "bfdfe7dc352907fc980b868725387e98e6c5f57d2132f3a87cf213e54ce1d40c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c5b861f22b2ee9f537df3f12e7d7ec0", "guid": "bfdfe7dc352907fc980b868725387e98e126c3596ffd508c37dff54a1633095c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b7a6090d4565038c5508811b1220249a", "guid": "bfdfe7dc352907fc980b868725387e980fe2801d88b02baf5a681216a6b90c55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fe7c0a03d8226a075825fb244b8b27c", "guid": "bfdfe7dc352907fc980b868725387e9893c03283a4f5ffcfd4ada2330fea676c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803179f12c8ab21fdb81c29d8d696c910", "guid": "bfdfe7dc352907fc980b868725387e98cca95539b78f55171f2d602e4be19f75"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f026ed6637fb665edced59fa86a7035", "guid": "bfdfe7dc352907fc980b868725387e98560ae000d861dd2d6b2d3185d7ffbf5c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98267571ed26cdbd6991eb42b7bcb6743e", "guid": "bfdfe7dc352907fc980b868725387e98190faf1fb12aae2481b3a8285b27a6c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985acee10d65c955026146b5a232a851a0", "guid": "bfdfe7dc352907fc980b868725387e98dd376cd1e39c4dbdfd7fb868a9d2aa8f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ce8de6a0b517538b256ac4edd241b84e", "guid": "bfdfe7dc352907fc980b868725387e985c49642db3c08c1dee7b3d5a70f915f8"}], "guid": "bfdfe7dc352907fc980b868725387e985a2537a2c5620a618098915521891ab4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e987aaacf58af133c3e1922aa092474ec17"}], "guid": "bfdfe7dc352907fc980b868725387e98caf7d9495fde7830694ae8aab852a293", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98ae7ec18a583b1fb107a7c7781c949880", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981f0a8508efd61386103314ddbb82a530", "name": "FirebaseAppCheckInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e988e935c81efc4686179f554b8fe37864a", "name": "FirebaseAuthInterop"}, {"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e98dd3a6a519ed4181bf31ea6bc1f18ebc5", "name": "GTMSessionFetcher"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98f1e09b32067e7d86144abdaf0d62fddc", "name": "FirebaseStorage", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9861b2e033fd71c20add064527e8a82b5a", "name": "FirebaseStorage.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}