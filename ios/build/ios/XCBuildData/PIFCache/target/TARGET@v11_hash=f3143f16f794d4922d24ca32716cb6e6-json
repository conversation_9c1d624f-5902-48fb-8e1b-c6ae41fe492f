{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989b239d6716912a49f63366e9a9469ad8", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981bbea9ed5c5ccfc982d0de62ff9a1c4e", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9895f10f2c1ab679a224130e8347eefce4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981d2e688aca7759675e2cdb62ae2bc209", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9895f10f2c1ab679a224130e8347eefce4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREFIX_HEADER": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-prefix.pch", "INFOPLIST_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCoreInternal/FirebaseCoreInternal.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCoreInternal", "PRODUCT_NAME": "FirebaseCoreInternal", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983c6faf8ac98ba00a64cd0ef3f03044cc", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989d2ec1c6b5f555070b1d9a38e8138c3e", "guid": "bfdfe7dc352907fc980b868725387e982dee45231e483fb34321edd86464f82b", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9806e288710c7b5b245ccba67b5df1168f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e989737edd80a4dfc2b38ed3288239b538f", "guid": "bfdfe7dc352907fc980b868725387e984c1e09b5e2fd7628a0c8d9b347598b30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98201785a82c1860dc40f2a169a8a68b53", "guid": "bfdfe7dc352907fc980b868725387e98f96d4f558c66891b6138d5efa4402306"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983e9e67bec55c16202953b6d5e99dcb3c", "guid": "bfdfe7dc352907fc980b868725387e9867b1881094302c4318e5bb70871a1f00"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b46e7039fe8df6030d3a45fbc5a40b58", "guid": "bfdfe7dc352907fc980b868725387e98b504fb7ee19975bed8f05ebc9ded5f7b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98426dc4236586e014f7ad61e653fb04c9", "guid": "bfdfe7dc352907fc980b868725387e983eb069be2a45c7ecb73c3d48266efb77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986eb798b16066d47faabb791e1916bbb3", "guid": "bfdfe7dc352907fc980b868725387e983a4f3765fde9064bc6370f14a90cd296"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7d6fc23d9417d2360dfed5c963acde5", "guid": "bfdfe7dc352907fc980b868725387e980ed4d529a64488e1a714497bb47948c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c62f27354cdce4093273e97d0fc39e3", "guid": "bfdfe7dc352907fc980b868725387e98a2260462ef575d9bba194ce4bf774974"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989943d3b6d2b1263bea07755d60514207", "guid": "bfdfe7dc352907fc980b868725387e989147ec1257187b680abbb981f73132af"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891735c9ff49d5f1833b3caf61430e041", "guid": "bfdfe7dc352907fc980b868725387e98981210aa8e3ba794f2685acad0e0586f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822016de217d07054216715e86ade8a7c", "guid": "bfdfe7dc352907fc980b868725387e981d68728f9f663cc60d14e67da9d8eec7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819b51019a97f9bd02ae40589178edd3a", "guid": "bfdfe7dc352907fc980b868725387e98551350f549d3772632e9126a928e8c25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882ebbb4ac7d74d18905394d61dcee4ad", "guid": "bfdfe7dc352907fc980b868725387e98cf7f4c308a8494f77e21a09979d50ec6"}], "guid": "bfdfe7dc352907fc980b868725387e985d667865954093817481ae8359cb57b1", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e9892c7e4c5be7f7e880e119ed2abe4aa69"}], "guid": "bfdfe7dc352907fc980b868725387e98af6252b03dc2efc3deaaa51a8e6fe9ca", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e9849eac4cbdb690a1eace5f0f766c8af9f", "targetReference": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f"}], "guid": "bfdfe7dc352907fc980b868725387e98810acd73bea452746fca5bd55dd11e32", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98e5b592b076e092ab7ac9d9b5c85edc6f", "name": "FirebaseCoreInternal-FirebaseCoreInternal_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e983d86e87924acfad2934921ce7ad9fbea", "name": "FirebaseCoreInternal.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}