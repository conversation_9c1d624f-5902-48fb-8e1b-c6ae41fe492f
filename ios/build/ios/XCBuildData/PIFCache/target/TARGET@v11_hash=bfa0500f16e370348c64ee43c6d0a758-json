{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98565e40fbe4af3f2d3e4167da45c608a9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHONE=1", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98cba9c6671c53c8f98fb4dc1723152b86", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986a3aedcdc515116c52fb8954666e7ae4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHONE=1", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9888d505e706bbfce3fffafc6de3597f20", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986a3aedcdc515116c52fb8954666e7ae4", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "GCC_PREPROCESSOR_DEFINITIONS": "$(inherited) PERMISSION_PHONE=1", "INFOPLIST_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "15.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseFirestore/FirebaseFirestore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseFirestore", "PRODUCT_NAME": "FirebaseFirestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e985e2b7fc0cf810330a1c34247c2f4bfe4", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fc16383428345d962e6105588828110d", "guid": "bfdfe7dc352907fc980b868725387e98ca20a0ce57d68c25b676b7d7341a9b64", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801d776719cb157a903483f5541ef189e", "guid": "bfdfe7dc352907fc980b868725387e98d1917c7582bcf8ab555e65220508d239", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980eb45c8e9f18377b1153fba7eb9b594b", "guid": "bfdfe7dc352907fc980b868725387e98fcc52b3d591270beb70f444b4412daea", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888c6a9a0641148e3d760a4e5c2870c9d", "guid": "bfdfe7dc352907fc980b868725387e9821b6ef15d9692e7f676551d54d284549", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987bc3e02e463cf81c43caf8a95fe4b190", "guid": "bfdfe7dc352907fc980b868725387e988e69ff54ce21bd1b2af62e87b8335622", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98466bda68e43a272e341bb0b9c6eed7d0", "guid": "bfdfe7dc352907fc980b868725387e9830287b2daebadbe73f9b849033bf837a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b762be9a5aabc5292f7cd1edebc63a77", "guid": "bfdfe7dc352907fc980b868725387e98a66c389b341fc8654dbeab5e81e7942f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830a6a222755d620152093d2f8e9be0b4", "guid": "bfdfe7dc352907fc980b868725387e9847adc115d078a683475e83dfca87dd33", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a305fd83519e432dcb2fe4edf8e1579b", "guid": "bfdfe7dc352907fc980b868725387e98857e1f7e6549baec18c2773a84c04902", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e841adc9c9d37a64732ce41c88fac11", "guid": "bfdfe7dc352907fc980b868725387e98154230c77631a47c3f4696b66b3bcd24", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f6e3001e6fa68eb0f707acfc9fe8e668", "guid": "bfdfe7dc352907fc980b868725387e98f8eb6f8f9b0610612c261c79c50e0e4b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a7612fe50030be6bec66f876e2f056e3", "guid": "bfdfe7dc352907fc980b868725387e98cf73b5dae7ff8c306500232057c97fc4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9874b1168c2538d82aa638d84bd555cedf", "guid": "bfdfe7dc352907fc980b868725387e983528cadb21df36ece5eb7a6d053cc03a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c0c5fdf1ee0998f02695837b44301e2a", "guid": "bfdfe7dc352907fc980b868725387e98bdbae29b23b10f8824569bd1de783111", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988ca8ce207a0e1212be34cb2ed706919f", "guid": "bfdfe7dc352907fc980b868725387e98a1f44e010c79af620e092b06637d42a5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987aa07994c1af9b1ec0872db26c7c543c", "guid": "bfdfe7dc352907fc980b868725387e9848bb81359ce939d5338a0c150b62c0b5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c247820b885b45788d884135c14fa80", "guid": "bfdfe7dc352907fc980b868725387e9836826c63b7ba573afdd39896f9ebc421", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983dda5bb570110e776a8e2766c3a57014", "guid": "bfdfe7dc352907fc980b868725387e9806be7cb589172232cc47fe1b5879e3bb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988fe3cfad841a96e4943758ddd3974113", "guid": "bfdfe7dc352907fc980b868725387e9836b4dd96c095c809e0b2ee02660b91d5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982cae27f97f3874d4facba4521f51055f", "guid": "bfdfe7dc352907fc980b868725387e989106ea221567a7163923d6d1d55b13de", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802262c9ebe72d821fbdb1f8c92ef7d96", "guid": "bfdfe7dc352907fc980b868725387e983014e65db1ce160af3663ef816589e6f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989c5556d44848076d853b963320e82ebc", "guid": "bfdfe7dc352907fc980b868725387e983a142fbd2cbfd780a4cf05ae06853f5e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5f595196081cb9257429e850555370a", "guid": "bfdfe7dc352907fc980b868725387e985c2cacddafc79dcdb1fd7fba679b478d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d64c09606066ec5dc6830e92ef016bfd", "guid": "bfdfe7dc352907fc980b868725387e982c0a2ce5a3c60dd0992a4913f4bb87e0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4ec97cc51fce77d51e9c6b68a50bd04", "guid": "bfdfe7dc352907fc980b868725387e98532381edfd264ff69d6e3f0de43dd3f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836223bc892cf38f231361d9be84a6dd1", "guid": "bfdfe7dc352907fc980b868725387e9814b806850cdf82dca08c5ceecf053678", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b0b50f36826947590f6f41f8c3f9337a", "guid": "bfdfe7dc352907fc980b868725387e986e7fec1318e1ccd50337987cd1f97743", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a9e2c5e1949229008e6030ca171c4df2", "guid": "bfdfe7dc352907fc980b868725387e98531af080d03e03eee19f9d32546fdb50", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9b10535bd30e6b0709cd2d26e69a293", "guid": "bfdfe7dc352907fc980b868725387e98b91980c6466035728ea16bb59e57e7c1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7ea2aeccaa5ee28e01b4532dee72def", "guid": "bfdfe7dc352907fc980b868725387e981111d07854f89130aee573392b35ffdd", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9844568ad5eff16bb4cb5da713b80d3e39", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e981afe9f9a853332a2d0e72eae314c903f", "guid": "bfdfe7dc352907fc980b868725387e98138bed97236bab9f598b84a69ce9e4e0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ff274aca94c8449ecf431f7fc8b9b72a", "guid": "bfdfe7dc352907fc980b868725387e9828886693a93518c5a84dd0e60269b594"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980139f5729d516810837cf8a156913366", "guid": "bfdfe7dc352907fc980b868725387e980f160e49787f3833b86e9b25fefa564c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f23fe7926e9fc59a1466c89873f073ca", "guid": "bfdfe7dc352907fc980b868725387e98508e3b4108a3ddc11b4ee4dd17cc1caa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890f0122dc30ffcab8f30f9d75028bfb5", "guid": "bfdfe7dc352907fc980b868725387e98fa8354c506e8ad079ee8f10c0916b5c7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9814bec18163cb880b644d068e2d4a252b", "guid": "bfdfe7dc352907fc980b868725387e988d8ddae9082f99bcd5f2ba6e1f0552db"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bca2dc79ad1e247543d1b7846a413803", "guid": "bfdfe7dc352907fc980b868725387e9847ad1e26179c36d7173b73cd5ca84f77"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98876308777b6db3454a60710507b49101", "guid": "bfdfe7dc352907fc980b868725387e98b18d567cb99ee99d5560921ae43a92f0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d318a1f970cdf9a98166a9afcd05446", "guid": "bfdfe7dc352907fc980b868725387e98b5a16fa27075bb3160ed84cbaf94def3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d255956349c81a580d89b8a9b3f0b5d0", "guid": "bfdfe7dc352907fc980b868725387e98da6d1e8d5f738e87d40d58e82e5bb09c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7787a6a75559dbc9c1bd6c232598abf", "guid": "bfdfe7dc352907fc980b868725387e981293b0b6c87715afee180343f46c3e92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9caa0f6688b0659d0b270206db57df8", "guid": "bfdfe7dc352907fc980b868725387e98f086fa810f7855ce85889b2e226a2283"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d36065e1f450a25c3ea75a2189981f1a", "guid": "bfdfe7dc352907fc980b868725387e98ba882d4e5196d6691340b1d92c82d76b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1b0d2c385ff2401065419b84f173812", "guid": "bfdfe7dc352907fc980b868725387e98388272c4c1a1f704f2f3f9e190dab616"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fea8981fd20fd3fb35e2529e8ebcce57", "guid": "bfdfe7dc352907fc980b868725387e983943000abafa544660dc4d0f4356e626"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985400d57e5e06f1985487ea76cc77d731", "guid": "bfdfe7dc352907fc980b868725387e982fd0f5e45d20671f2a1bd81e26b0d6d4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9888ef30cd9a38de9c0323901cc9f2f3a8", "guid": "bfdfe7dc352907fc980b868725387e98598930744fcab7392f3fcba10a62b76a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd185ec831c4f4cd3b922261db1db06b", "guid": "bfdfe7dc352907fc980b868725387e982542445fc4b144924dc281bfca9b1b64"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9899d2590da466667b56ade62746b9b08c", "guid": "bfdfe7dc352907fc980b868725387e987c6e55dce51a62b7432922bc0c66015a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98806f4b399313f7d9b432dca28d8b1d0a", "guid": "bfdfe7dc352907fc980b868725387e9885138df57c2dcf09e7da405b80d3aa4a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98792af55c774c6780f26ad0abf150582e", "guid": "bfdfe7dc352907fc980b868725387e9822e6a41297b982882be3d3084710450a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98232e788a33f3f3afdf463a73b87ae16a", "guid": "bfdfe7dc352907fc980b868725387e98cc1bc122ca880018a75bdddb8bad423a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840c6521dde850c0cd75eb82491461421", "guid": "bfdfe7dc352907fc980b868725387e98a0183f388a78c158af9ac792379c6232"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fddebbc43987cbc6ae59ac4a0e25b650", "guid": "bfdfe7dc352907fc980b868725387e982a2c4efed72d9dde743bbcd4e8904bff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed34cfcdae63b6933e19f59691143f5d", "guid": "bfdfe7dc352907fc980b868725387e984a91ff0c4066d05db2b6c9cbf4635d5d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984f6761aefe3c9499af9bf95bcd6aff24", "guid": "bfdfe7dc352907fc980b868725387e98ec3f29f8e6266aea8066e64e31806b98"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98649691e5fec6c3831376e2d4e4ea5306", "guid": "bfdfe7dc352907fc980b868725387e985cb3f570e10b43e67521dfcb52a96127"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981d11ad93619fa39db13fc8d6bd619e7f", "guid": "bfdfe7dc352907fc980b868725387e98a15db0fb23aee0b0adb9ff8f9d1eb904"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd723f217b75581101a43e49c1425b04", "guid": "bfdfe7dc352907fc980b868725387e98a2b6b3040c35d12e2adc0b1d5ac41899"}], "guid": "bfdfe7dc352907fc980b868725387e984e6d93e1e6816455ddd51a9ff08fa564", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e988ffa9193dcd072cf51c133f3de0bbd81"}], "guid": "bfdfe7dc352907fc980b868725387e9805e894b7971d2978d36834a8cd3f4af7", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98aea796c52a3d7393ff5896a4c231b1ad", "targetReference": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340"}], "guid": "bfdfe7dc352907fc980b868725387e98c738ec9b05f7d919164257463bb9251c", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore"}, {"guid": "bfdfe7dc352907fc980b868725387e982fcb5e27d041e48b96b3ab14ce32d5f2", "name": "FirebaseCoreExtension"}, {"guid": "bfdfe7dc352907fc980b868725387e9823d9e39e74dec872f3c67d98403b9340", "name": "FirebaseFirestore-FirebaseFirestore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98919212c22943df12241906dd601cdff4", "name": "FirebaseFirestoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e982a62e2c60acb8d344a6411a0606a13d4", "name": "FirebaseSharedSwift"}], "guid": "bfdfe7dc352907fc980b868725387e98c075cc473fa5680b867d51f1363214ff", "name": "FirebaseFirestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9809dfd848e2259e061e90089e1647f5b7", "name": "FirebaseFirestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}