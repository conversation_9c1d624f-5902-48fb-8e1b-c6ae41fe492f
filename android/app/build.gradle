plugins {
    id "com.android.application"
    id "kotlin-android"
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id "dev.flutter.flutter-gradle-plugin"
    // Ensure this line is present
    id "com.google.gms.google-services"
}

android {
    namespace = "com.fastyorder.app"

    compileSdk = flutter.compileSdkVersion
    ndkVersion = flutter.ndkVersion

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_1_8
        targetCompatibility = JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_1_8
    }

    defaultConfig {
        applicationId = "com.fastyorder.app"
        minSdk = 31
        targetSdk = flutter.targetSdkVersion
        multiDexEnabled true
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    buildTypes {
        release {
            // TODO: Add your own signing config for the release build.
            signingConfig = signingConfigs.debug
        }
    }

    applicationVariants.all { variant ->
        variant.outputs.all {
            // Ensure processDebugGoogleServices runs before mapDebugSourceSetPaths
            if (variant.buildType.name == "debug") {
                def processDebugGoogleServicesTask = tasks.findByName("processDebugGoogleServices")
                if (processDebugGoogleServicesTask != null) {
                    tasks.findByName("mapDebugSourceSetPaths").dependsOn(processDebugGoogleServicesTask)
                }
            }
        }
    }
}

flutter {
    source = "../.."
}

// Ensure this line is present at the bottom of the file
apply plugin: 'com.google.gms.google-services'