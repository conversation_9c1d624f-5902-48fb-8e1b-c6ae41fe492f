{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98cad1c868c75a9c1bea4f0c6a0bd7705b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983dfe37ab894d90b53b9de35b9e18a8be", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dceef1a26060bf57dd27ee5a6b756c2e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9834655fab906f0bcdacc6f09bd17480ee", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98dceef1a26060bf57dd27ee5a6b756c2e", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/GoogleUtilities/GoogleUtilities.modulemap", "PRODUCT_MODULE_NAME": "GoogleUtilities", "PRODUCT_NAME": "GoogleUtilities", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981e8b43dae6feafa5775499296fe10289", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98c1eb29227935f096f813cedff460f2d7", "guid": "bfdfe7dc352907fc980b868725387e9806aff2bbe8b6708a73b58e54bf4df5be", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eef17fded385fb068ef59c8475e926a2", "guid": "bfdfe7dc352907fc980b868725387e9869542db03c46cb9d58268031702d22d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985ebfb452c3dba257e4635d073f037e89", "guid": "bfdfe7dc352907fc980b868725387e98aa9ae41d9da98be6bf7a8f8fd81f9a15"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7f3a39e59cad0c47af46dbbf7207b3f", "guid": "bfdfe7dc352907fc980b868725387e98e685c550dd56d7c2d03314cea36a51a2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c75f5fc5838ef69e95d20606b4e9731", "guid": "bfdfe7dc352907fc980b868725387e98d1a28cbd1099dc3a048de65083b56765", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a4a7b21ab158453fc2a7d437a42f5713", "guid": "bfdfe7dc352907fc980b868725387e988a9f86b799822644ea621ddedc0a7046", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a1eec7b6792d345d56c8c6e6b1cf7249", "guid": "bfdfe7dc352907fc980b868725387e9808b9e492e3a9551e4ac343d8ec31dfd4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989fde6a4b8d700fd06fd2bceb6dba718a", "guid": "bfdfe7dc352907fc980b868725387e9822b23087d2a6f4e8921c5907a4dcf895", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9804973e064c08f0c1c1f6e59104dd7620", "guid": "bfdfe7dc352907fc980b868725387e9851f238e59ad9ae87f9b618411f616d19"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9847a4231fc4fd7ec96695a706a08b0e96", "guid": "bfdfe7dc352907fc980b868725387e984bd92e0edf0cadaad1a21c9afd39a83a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ff33c918161910c043b238683506b4c", "guid": "bfdfe7dc352907fc980b868725387e987c3b486c83da6c48c95b3180e17035fd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985df0943316ce290b6498c23b3a1b31f3", "guid": "bfdfe7dc352907fc980b868725387e984a8be6c6055c71f7e496e0c5eed26572", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ded5233c0817c8303701240c2b8a28a1", "guid": "bfdfe7dc352907fc980b868725387e989346ba372eb9539ea15a5378519f209c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f882145503397c7766909c2d08f4e659", "guid": "bfdfe7dc352907fc980b868725387e98dfb75340a33a64841b8d0da147d9c2d9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdeafc0f59b37fa287e4acd78ce0b985", "guid": "bfdfe7dc352907fc980b868725387e9841d27391eac1a4517904dd8ff26cdf24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98358870d36f55a1e5edba3ca6f09aab0c", "guid": "bfdfe7dc352907fc980b868725387e989171ddd373f1b48317fafc26081a1393", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868f9679f9e3ac3f98e31d0788697d27f", "guid": "bfdfe7dc352907fc980b868725387e988c31e55bd0f411d8182adf82a7b92983", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eeeb7f056f384a3a61f287cc796a5955", "guid": "bfdfe7dc352907fc980b868725387e984e1e7d91aa7e61b382b6c0dbc06c19ef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9838f93370a4d8c69f05199faf4a701760", "guid": "bfdfe7dc352907fc980b868725387e9860c3df5b0f8e398f2b46a3b5c41de22b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec61728d031e526fe3206ad339729119", "guid": "bfdfe7dc352907fc980b868725387e981f2f4451540714a4a40bb35274695ad1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3cae886de9430942eda71629bc1a263", "guid": "bfdfe7dc352907fc980b868725387e984fe7d6a6cd4a9e2ed5200ed0a37de9f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe41425164589f9d736540967aa8026f", "guid": "bfdfe7dc352907fc980b868725387e9898e65f0ac21aa7e8d5f2c12e91be8a8c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983ad718bebe8291a1927c662449990a10", "guid": "bfdfe7dc352907fc980b868725387e984a5c27f925de68b24d771b420f700a50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a032ce5ce814dbfe8f69841d35f3c413", "guid": "bfdfe7dc352907fc980b868725387e982b18567fd54aed2cea5b0a711e21fa2e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ac0140053339dd94d5c088713ed8f22", "guid": "bfdfe7dc352907fc980b868725387e988cd16ee7f207b47c8eb99743cbd71520"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846f1b72a122bd262e12428151715f1a7", "guid": "bfdfe7dc352907fc980b868725387e983d92b122f41d5c1cce8ae6e411f211d7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9e3d0bcc4a34b6212f2c1b89ec50d19", "guid": "bfdfe7dc352907fc980b868725387e9873ab8d5174d29eb3ef18d446b32d0cf3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd55c94cf9ca8c082741098abf804a0b", "guid": "bfdfe7dc352907fc980b868725387e98e64567b085d5f520ef0b79bce4dc2394"}], "guid": "bfdfe7dc352907fc980b868725387e986b6b1351df850fc15705f7e3a2b3257e", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d8b7614dbdded4ef5dada1f75a5d8f80", "guid": "bfdfe7dc352907fc980b868725387e98757652a8ad0fdc6ecdd73b7232f6d3a2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886c7bfc5dc0c5a32a12d43327108b561", "guid": "bfdfe7dc352907fc980b868725387e98a0352dd4ef31f823c63fcf3c3d805c96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c00787c88cc904786643043aa2b82682", "guid": "bfdfe7dc352907fc980b868725387e9879b19631e4a6ce013dd99e3c2a28c991"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983fa2c17ad0b9fd6a67e8ec5d691908a2", "guid": "bfdfe7dc352907fc980b868725387e989cfb6cca413f6bdcd017e9894fe38dd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986c6b37650230baca40e408d5d1e33dc2", "guid": "bfdfe7dc352907fc980b868725387e98f626e6f5c20c3d6dcf668748fa9268b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981dfb1feff61585b5c1d040c756bfc0c8", "guid": "bfdfe7dc352907fc980b868725387e98144507e527d9ad5311f8eb1c311220ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9837de2de6718dc16bc9906304163dad63", "guid": "bfdfe7dc352907fc980b868725387e980eaaaed17e482b6f14227396aecd2de6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846c5c9b5d01ef92d6ee967b3542e26c4", "guid": "bfdfe7dc352907fc980b868725387e9893ae6e7e114ed7efc50752f7250d09c2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f11c40436cf4e6f834dc17299a43d02", "guid": "bfdfe7dc352907fc980b868725387e98ea4b1b8728c77021f42413604cedb584"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c39190a02c5fc7106fea198492603d9d", "guid": "bfdfe7dc352907fc980b868725387e98853ef2b21a2a6fffa99c7330d59d40c4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fee1aad195afff8c61d582446633f703", "guid": "bfdfe7dc352907fc980b868725387e98107811a740bb0448386470323bffeae7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98306ea77216a5238b4f6df3060a5d9468", "guid": "bfdfe7dc352907fc980b868725387e986a8db4abc3c3b310f1442661a8070ae9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877103da282ce823dde5b1a9110deb8b4", "guid": "bfdfe7dc352907fc980b868725387e988391eba526527f66a3202a0fadbb6785"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836cfb70fca1d9887cf7e02622096f00a", "guid": "bfdfe7dc352907fc980b868725387e981b276232933d6f0f588c63b31edc1cdd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898e53eb40886f09d9ad9010657803f7d", "guid": "bfdfe7dc352907fc980b868725387e98f65454a4912bbc5239a9704a6bff0f28"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984cca5e0b6b141875de41a74541b98ed4", "guid": "bfdfe7dc352907fc980b868725387e986f054512571b98a10438076822570fc3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98085dd9322b6743d050139dbaedc68c78", "guid": "bfdfe7dc352907fc980b868725387e9893b7de5babef156d754a15a15e286101"}], "guid": "bfdfe7dc352907fc980b868725387e983aebecc51fb8191ec2ef6ec2bc970c70", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e98dea0df965998a1bc06844cd4e2c075da"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857517583cea0339c53d5dd4c2679b551", "guid": "bfdfe7dc352907fc980b868725387e989ff3ceda4066f3613e7cd9328b11437b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c70f24a71cc0fd6390913ab7a7961614", "guid": "bfdfe7dc352907fc980b868725387e9839d71a5f3f6359353a72e77ee93d8050"}], "guid": "bfdfe7dc352907fc980b868725387e984fff7b162f9c1b0a8a22f919caee1e40", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98a2490082470720059d026abdfbfbd051", "targetReference": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4"}], "guid": "bfdfe7dc352907fc980b868725387e980047dd0c8765a267c860f402f51fe81d", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e981a9fac6eb9c80f8eed49fda0531af6a4", "name": "GoogleUtilities-GoogleUtilities_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98ca49ca851f2777b997a3e74ccb860358", "name": "GoogleUtilities.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}