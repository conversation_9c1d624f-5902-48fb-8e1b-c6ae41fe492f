{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e989abacbf67d11ee9b0c8b8eaec6705ac8", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e989f30337def316ec20bda6cbab71ca2ac", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f3cdf06963ff7df27fc9c692c6624b7", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984109f87592dbdff3ae1c73d1805981d5", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e980f3cdf06963ff7df27fc9c692c6624b7", "buildSettings": {"CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/PromisesObjC/PromisesObjC-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "9.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/PromisesObjC/PromisesObjC.modulemap", "PRODUCT_MODULE_NAME": "FBLPromises", "PRODUCT_NAME": "FBLPromises", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98810b1408140770c2004e55cf4e015c35", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ff11ff11fe549ac8bb8d8debc1e44670", "guid": "bfdfe7dc352907fc980b868725387e9801133edb2744f640a82debeb99e4f4f6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984e5b2c8cc003bb16dcf2dd710c4e36d2", "guid": "bfdfe7dc352907fc980b868725387e9896a5a4ba2a13140f3c1df2496b6a9d71", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bdaff30403a415c1bf212357550a6d4f", "guid": "bfdfe7dc352907fc980b868725387e98ea0b4b4747b77adc9bfd3b1913f43959", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a360e934cdc5012a9650f3ceae402a3a", "guid": "bfdfe7dc352907fc980b868725387e980c2b528442fee26f4bd5fd410fd706e2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c422555ffd89537045872cd25c958df", "guid": "bfdfe7dc352907fc980b868725387e98b1a7eb39d04e8a4b005d478e1d4c4896", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98059f59b02c0ca739fd96e05ee4d98b20", "guid": "bfdfe7dc352907fc980b868725387e98df27193881e0236001de95e5656773f8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891f5a1e12f882cd0d8fe39503b31bda6", "guid": "bfdfe7dc352907fc980b868725387e98cdb580611de9622e5be0b2b7b0708280", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e72140a7c60301a0e7ccb42d184b84c0", "guid": "bfdfe7dc352907fc980b868725387e98a667a5bf7064c41b5f95e7e488cf4bef", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987099eb23800277a556eacc28036f8bd3", "guid": "bfdfe7dc352907fc980b868725387e98252695559b88c7a3dc897cff8ebf600d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989af0ca593fc064f42f246d9488635f7b", "guid": "bfdfe7dc352907fc980b868725387e98af5a3e10e2603541e814aa882e8a0554", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c1ebb540e6eaee17a8d7b1dc632a28e", "guid": "bfdfe7dc352907fc980b868725387e98889cbbc10208c42acf0a3e201e6fc33b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983b4560d71722fc78de5ecd11599cb066", "guid": "bfdfe7dc352907fc980b868725387e98135101aa7fc1b80f958cecd8d2f6b124", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b82a923bf371002168fb709ffbd659ab", "guid": "bfdfe7dc352907fc980b868725387e9805f6ed0139ea3fd431aed8c5432e6716", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de2b6aa3071cc36381d1a84b451a060e", "guid": "bfdfe7dc352907fc980b868725387e98c11f932b5ecddf1d40bf165349060e03", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98adfd43897f17c74b617f9e9237fa6cd3", "guid": "bfdfe7dc352907fc980b868725387e98a92ca408c66e7aeb36e0edf7e82ffd6a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f9c35914460ff9d447a6e8d449a661f0", "guid": "bfdfe7dc352907fc980b868725387e980e7380a661ad9450df4c039795c85b27", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c670c183dc53a3c8226b4c54e5b21ecc", "guid": "bfdfe7dc352907fc980b868725387e98d1b9865eee098f829a8af2f0cbfdcaa7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e6d1bcef8a5cf098203cfdc556b18dbe", "guid": "bfdfe7dc352907fc980b868725387e9884f92e5f42407fdd2ec6b13408a1d053", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b9e91fd739dadf3a3b140955b12e22a8", "guid": "bfdfe7dc352907fc980b868725387e98c27864f6beb414eeffd49ce7944da35c", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bbdb121e2513222fb263e7351d6aba91", "guid": "bfdfe7dc352907fc980b868725387e9892ea664bc2379ab90090dcb8ca879ffb", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985545493ef5642bd85d18efde61bc9e2d", "guid": "bfdfe7dc352907fc980b868725387e9854e8269a5d07f473d3e150a4c589f2a7", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984be45e6eb598cc30ecd9b344e525451c", "guid": "bfdfe7dc352907fc980b868725387e98e4541d991712d0008fa43d8ce382e0cb", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e989e080350776ce5667bc2505aad2ae710", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e988151c4fe5c26fff4fbbe33f3fc3a2cb0", "guid": "bfdfe7dc352907fc980b868725387e98c99a768c6b08de358fcbd1453361cd2d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9890906859dc7a4e9a48429ca983a50a29", "guid": "bfdfe7dc352907fc980b868725387e989ab1d4f2969c17790147944cd723bc57"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9876f545d210c247bd0b7ef122127ba06d", "guid": "bfdfe7dc352907fc980b868725387e98c9bb319d148e084937b540117eea798c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d1dfa626279bfbdc97af8db918cd929", "guid": "bfdfe7dc352907fc980b868725387e9819eeb9c18b53d948602f1cf08d10a433"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98839348c51f469d7201f38b7db353e649", "guid": "bfdfe7dc352907fc980b868725387e98318d3e49c19c339a1c83a7ab697aaf48"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802cfcbcebcd2cf23ded551c5512d20e3", "guid": "bfdfe7dc352907fc980b868725387e982ebe0cf4aa3ef71913783fb70364c44e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9892f95ae9b9e06213d18e677168d9536c", "guid": "bfdfe7dc352907fc980b868725387e988ec978ab9e31f160ed980e4aac1240f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982505d95ffb6c6a5a3121c01c83a8210f", "guid": "bfdfe7dc352907fc980b868725387e9845ed24cea651f9ac26f55af7b8a1a02e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d992883deec579d32c41ea6a726cd23e", "guid": "bfdfe7dc352907fc980b868725387e98ddef1b31275c251d4153fe04ff48dd45"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a99a3e05cc4da7e9e150b3416a56625e", "guid": "bfdfe7dc352907fc980b868725387e987d439a30437f9c4973ab6d04107cbcdb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98523feadaa4377642b72768c3908b5c38", "guid": "bfdfe7dc352907fc980b868725387e987c8d9d053b483824dd7342ddfacf108d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d7cf9dece3299eda8483c370aeb59ca9", "guid": "bfdfe7dc352907fc980b868725387e98afe3da6c7994fccd7dba0b33ce314ec8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9ef1b4dd86a5465d296ec53f72f8aa8", "guid": "bfdfe7dc352907fc980b868725387e98107a4818ce844f2ebbf56806e0deb689"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0ca5af6d4a9016c039bdbdb44752960", "guid": "bfdfe7dc352907fc980b868725387e98e132f545e095b993c830a92beda68a1b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98efae4f602db6b838538f7d8f7e8ad505", "guid": "bfdfe7dc352907fc980b868725387e98cae518450d1b701bb2d8ab5209dd1c12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ff05a576d589576a84e1af49edd8d7d", "guid": "bfdfe7dc352907fc980b868725387e980a0cf9f1a8a2e8a87d067e8c7cd6559d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98207407288556521f97661bedce81c292", "guid": "bfdfe7dc352907fc980b868725387e98e7d2ab3d61b28271ac410cb7a088e6f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d9818d08aa81a9795c7b05b28539e1d6", "guid": "bfdfe7dc352907fc980b868725387e98aa0a29a365304366f7a76054b89b3957"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987b3f6e23750179e9cbb66a3cedc399b8", "guid": "bfdfe7dc352907fc980b868725387e98b502eb85f487dc7588a85762c7f88463"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982d17c8d53486e0768f0ed5f2b3797a50", "guid": "bfdfe7dc352907fc980b868725387e987fca4ea4b4b6f5054685fd25053f27ca"}], "guid": "bfdfe7dc352907fc980b868725387e9835daa5b3677bbca6aa0544b6d2dbaa35", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e988dd54857df1820e0318ca255c970b674"}], "guid": "bfdfe7dc352907fc980b868725387e98d8d57a701feaea1ed4e546cd841577b0", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98ba609c184aa52aac5cf328c5182080c2", "targetReference": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823"}], "guid": "bfdfe7dc352907fc980b868725387e98479b9d5dc9ef9a0cb5509590a79ca2f7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98ad53226b339581a6725de188f2c8f823", "name": "PromisesObjC-FBLPromises_Privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98f10882e1684b8a3dfdec597bc0a47af3", "name": "PromisesObjC", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e981c795e45f8d875aac88217c6a2a95faa", "name": "FBLPromises.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}