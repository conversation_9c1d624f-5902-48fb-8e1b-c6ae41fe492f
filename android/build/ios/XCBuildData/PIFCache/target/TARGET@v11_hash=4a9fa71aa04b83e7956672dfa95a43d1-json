{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98fbcc7a8ffbdfd662e7e19fa31d03c239", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98116b4b75420997cddf9dd2e8b5853dd9", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981d04336710e31696def4a85c03f51af9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e009fe081783c0448fc151158650b23f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e981d04336710e31696def4a85c03f51af9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "INFOPLIST_FILE": "Target Support Files/FirebaseCore/FirebaseCore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/FirebaseCore/FirebaseCore.modulemap", "PRODUCT_MODULE_NAME": "FirebaseCore", "PRODUCT_NAME": "FirebaseCore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.9", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98a5083f99ce9efbb1c6e4520cca8120c5", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e986d536e4d514a4c2ee5c47749cfa73537", "guid": "bfdfe7dc352907fc980b868725387e98ae9280d673974a14ba70e99cdc2e58df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4d11fa2f136a2da8f6b8c162499b0ee", "guid": "bfdfe7dc352907fc980b868725387e987133a33377823315f3aa0b0966d0d651", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fbf823f61091e72da17534c37e374305", "guid": "bfdfe7dc352907fc980b868725387e98aa10fcb56f1d6b36393df018cede7faa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b6678cd43f3213fa83d7acb7990cdc6", "guid": "bfdfe7dc352907fc980b868725387e988d51fecbf7e0d839425c7b11e7918005"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da2132293f668aed5a0cfc9940c2d24a", "guid": "bfdfe7dc352907fc980b868725387e983be9063c044ea4e271575d67aa9c0944"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1ee5d3fe82063056bd036f434ead27e", "guid": "bfdfe7dc352907fc980b868725387e986e92088ab0ad3a3648a0f3ca0d337251"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b945fdd2f10c9e653c96a767e510d06c", "guid": "bfdfe7dc352907fc980b868725387e98349e27881771bbc2c2be61b158213e71"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ae4af2b67ed4079c5b1d3e86ff0b55b7", "guid": "bfdfe7dc352907fc980b868725387e98cc387e919a54efb27c54bb04f6c5751f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9854e52fee34957471b06d89f96b13a242", "guid": "bfdfe7dc352907fc980b868725387e98c80fcf65d08c14c67d25a6dfb2535e8b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd25b64fb8192067a2c8d85e336a2a0e", "guid": "bfdfe7dc352907fc980b868725387e986ba66508e297fb94f16e2d37c55b7c94"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e5867465423f70d6a78b002b2b1e4b1a", "guid": "bfdfe7dc352907fc980b868725387e982230e5d708cf8bc5cc383f7464cbba85", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e4ec5af50e178f1ca11447e5bfc0151", "guid": "bfdfe7dc352907fc980b868725387e98b33c463a9dae7448e18cdb7d8fe53557", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c2fa41317091f890645ed8c687699f2", "guid": "bfdfe7dc352907fc980b868725387e9819ad2960e583adab21251577ff3b7276"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c1fc60d4e98e784c38169ba8673149f0", "guid": "bfdfe7dc352907fc980b868725387e988fcd37e7c7376d45dee6e02f7583667b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988122069166c437e83f0f0b5f6e1318dc", "guid": "bfdfe7dc352907fc980b868725387e98e0b20ef7b537afd996ed3811df925bcf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98503a88a1b62b344f8884079539499d60", "guid": "bfdfe7dc352907fc980b868725387e98076dcd89bfee920bafbbb7016d583b30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983346662e6bfaec1bf8c63cb231af7209", "guid": "bfdfe7dc352907fc980b868725387e987201b67d35bcc235e375945db0505d76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dd11127632b4e97aafc798ea50504158", "guid": "bfdfe7dc352907fc980b868725387e988dff34c3404eb6ba5b966e274a28efcf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98423ffd74732ac4f1f26ad49ab807d111", "guid": "bfdfe7dc352907fc980b868725387e98722ba6bfdd80f5a0ce9a5c9327ae1442", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a669b3b54db1bd9ae6c014906a7236e1", "guid": "bfdfe7dc352907fc980b868725387e989c78815cfa9712d6ca266b8699eeb79b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f90dbdbe0d70c101d1bd1ed38f14bb9f", "guid": "bfdfe7dc352907fc980b868725387e98d54628900e9e0fd88e68d14830d2cce5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988bb7ff8d01f0b7e66601a434507836f6", "guid": "bfdfe7dc352907fc980b868725387e98e410dbe6f5cdceac8c65d4d38f172d3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca0f0ec2ada911f5ab3120127734573b", "guid": "bfdfe7dc352907fc980b868725387e98d3cf78811c88dd3b114fa43a1dc8145f", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e9860599946a1aa5aa9afc6c7ccc2eea882", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9803661c587e2f8f0e6689e4ce6ed42f1f", "guid": "bfdfe7dc352907fc980b868725387e98c618e25fa6f24b241f9f8ecff7e59e3f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819c33958b72f1f083ab9d9ca6ef36939", "guid": "bfdfe7dc352907fc980b868725387e985b896825d882eb3131122790eb8afb88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a450747ec822f42eb5e9df305b3cae4", "guid": "bfdfe7dc352907fc980b868725387e983666d5e77d817b66d91cfea888833d58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f202c4dcb1dc784b22ab0446f78e42cb", "guid": "bfdfe7dc352907fc980b868725387e9824add3dd11dafe24c75c2650d3261d05"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829f24bd582db0e94d9cbd1043b27878a", "guid": "bfdfe7dc352907fc980b868725387e98b1c4b6d6243a08372384ee2747cd173c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980ead6a87a4003b6886e653af80406e7b", "guid": "bfdfe7dc352907fc980b868725387e98a60a74ba202525cbf822ddd61f32b5d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b6eeec9239c1c01aef533a98f5329d66", "guid": "bfdfe7dc352907fc980b868725387e98f4b0b1e5030563424e1a2a7a953403d7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987706013cdd3800d72757250ed333d61d", "guid": "bfdfe7dc352907fc980b868725387e9870a8249a08fde9efcdf81ccd6694a7aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a28fe74a8997c02bf58db558e5371aa3", "guid": "bfdfe7dc352907fc980b868725387e984f64bbb98a9c3a62cc9b4a9f3e8f6a6c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dfe531d91712edcf7de539410313849d", "guid": "bfdfe7dc352907fc980b868725387e982c9bdff97b231e16d2df8a39236f8b25"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984c59e7f2e7094a67a1db03381e111a36", "guid": "bfdfe7dc352907fc980b868725387e98a7011d86d58622d06f603e83baaaaf82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3ec14ed5fabb653a5e3d3cd6e322fc8", "guid": "bfdfe7dc352907fc980b868725387e9845119652788c53b62b7297f5ef4c888e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884a5077e9affee296ed7cdb4db4ea0e2", "guid": "bfdfe7dc352907fc980b868725387e9810071d7d1dc50f0a35bba6483cf0212f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879c6e251d68ce510a3f2cb8eabc1deec", "guid": "bfdfe7dc352907fc980b868725387e98e51d241cdcf6d3dcd4dbdf75b109e0f2"}], "guid": "bfdfe7dc352907fc980b868725387e98bb26d2b464daf27d232c2aec507b8c31", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e98f703aebc32869f8b2ccb7c606f74c6be"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab997f728dfc73763c7be192e3d19493", "guid": "bfdfe7dc352907fc980b868725387e98e844d2554fda46ba0c0aba8bbc802be6"}], "guid": "bfdfe7dc352907fc980b868725387e98f6a87f56b9f4270a2c6b956c8172bc3b", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98e8a3d79ef79849cc176c06e7937e601e", "targetReference": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14"}], "guid": "bfdfe7dc352907fc980b868725387e98112282679ffe271a430bd9767b7de8d6", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98678fb6500ea02c78520816441717cc14", "name": "FirebaseCore-FirebaseCore_Privacy"}, {"guid": "bfdfe7dc352907fc980b868725387e98020791fd2e7b7ddc8fb2658339c42e16", "name": "FirebaseCoreInternal"}, {"guid": "bfdfe7dc352907fc980b868725387e98718890dfdac589615663a02d43d9af3e", "name": "GoogleUtilities"}], "guid": "bfdfe7dc352907fc980b868725387e98a408a4c1f668e62161cdeba76f57d50c", "name": "FirebaseCore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e988ae261e418baab0fdd0a48d117fe7fa2", "name": "FirebaseCore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}