{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986f6d663ee8a253300f6df3405c3132fa", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e983acee0263ee80cf24be6e20e3fe1d937", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9848b1a90d3bb3259aaf9026ac38400430", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d3c1f856451a44dd2238bb8e03f629a8", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9848b1a90d3bb3259aaf9026ac38400430", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/geolocator_apple/geolocator_apple-prefix.pch", "INFOPLIST_FILE": "Target Support Files/geolocator_apple/geolocator_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/geolocator_apple/geolocator_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "geolocator_apple", "PRODUCT_NAME": "geolocator_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981496f826cc976bf124c3e2cdf403a9f3", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e987b66a6d5004063dae1710e150a94eb97", "guid": "bfdfe7dc352907fc980b868725387e984b4e2b2d38ceb40598d0629a6a29a998", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a52db1819f50301db7329ad1a9d07b4f", "guid": "bfdfe7dc352907fc980b868725387e983e0f4f5b863174fcb32043e87b8e66a3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b284d546be7dd3ba195c14374c512245", "guid": "bfdfe7dc352907fc980b868725387e98dc580796952acfb7fbf0fded35f54e67", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c10c50512d538323662ca9f96d70866", "guid": "bfdfe7dc352907fc980b868725387e98d22586e69bf2aef8b039891bcc650665", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bced26ae783b8e389a2a76dab40734e2", "guid": "bfdfe7dc352907fc980b868725387e9865ebbb9e2407b80c90fed1db89b340e3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f1b7ad910ffd9baae41d9e036f3e8b1d", "guid": "bfdfe7dc352907fc980b868725387e9899cdbb3f5c2a220155a76e55140322ac", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fef1e2b3eeb84412a7c061e71a38ea0e", "guid": "bfdfe7dc352907fc980b868725387e98fd3712aa9d01e1fc775bfabbb3600693", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802911a8d462d45ad54cb508f975296b8", "guid": "bfdfe7dc352907fc980b868725387e98850ddc5ee2cbcd07af827524c7dfb78e", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826eadc4cf5646924cca10a7d157af3f1", "guid": "bfdfe7dc352907fc980b868725387e98036e607cec0f7f2b6292d3448d94d705", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98442355ea874b32058f13df12c5c4f27f", "guid": "bfdfe7dc352907fc980b868725387e98fa7d18e838630224541f4a32f8fe3d79", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983075460fb9d1946d7afaa1203d2b2ea6", "guid": "bfdfe7dc352907fc980b868725387e98d20f516538f062edb154dff2e9e72326", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827a650b541ab2b6dfe3145f0db77e3cd", "guid": "bfdfe7dc352907fc980b868725387e9863adc356814f9a68bae9d10e183d3dbb", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825d95ebb0a1ea5cf750d15c8b9449890", "guid": "bfdfe7dc352907fc980b868725387e98a562d3226230ecc9e150571e6a8ca86a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98745d3905ee57a03239dcb2fdc10e3941", "guid": "bfdfe7dc352907fc980b868725387e986a89fa83a23fb0ba50e0aaa9890b3cb2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b880aebf467bcd6e0c2e276a61741e89", "guid": "bfdfe7dc352907fc980b868725387e98c7a4d2f624029d7e36489918cfb486d0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2fcf0521bf0531a24c1063b632d48cf", "guid": "bfdfe7dc352907fc980b868725387e98840c991519783c8bc41b731ef77fbf43", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9826e90f658be4f1552539b87e51b0b38e", "guid": "bfdfe7dc352907fc980b868725387e98b6ea7a37cc9b1b085cc626894408a980", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98bf564ff1975ff472a8bc2c91f2bfcf9f", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ee325a01978c5abc6a28161071564f8c", "guid": "bfdfe7dc352907fc980b868725387e98fc2aeeff30372a8e9382bd9424da5e58"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c830b43614f58f19340bbd458d8f8d1f", "guid": "bfdfe7dc352907fc980b868725387e98ba368f5b46b86ed2f9356bbb228ec697"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985b9ce90db557e631d1f0ee16feeaf919", "guid": "bfdfe7dc352907fc980b868725387e98a07f46006e0e0b13acf0b1712cc73130"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9843a99a4a33862635091fe4ab6e588fb8", "guid": "bfdfe7dc352907fc980b868725387e982608d167b16e3c511c909caa418e1af1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803189869d60119cf70dccd3e5b88559f", "guid": "bfdfe7dc352907fc980b868725387e98185a926dec138453c5fabdc29d0c9a3e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e55f354806d1158adeb6789dad35800a", "guid": "bfdfe7dc352907fc980b868725387e9881210550520b1ee3bfa30fce3d413653"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f593cb0eaf6236649db3e677159bc6b1", "guid": "bfdfe7dc352907fc980b868725387e989db863b76450c07e6a3efeefea5d239e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f15f1de22ddcdbeb12292d56e309bc74", "guid": "bfdfe7dc352907fc980b868725387e986da211b77f3869a367c7cc59fd32c453"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f36f442a90af314270a9944d7e3a785a", "guid": "bfdfe7dc352907fc980b868725387e981d7eb2fc908b56d891d52aed5a4a5892"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9886f339d5fad220096ab8d79b278aa44e", "guid": "bfdfe7dc352907fc980b868725387e98f50096c22f8337a29ee522ddda53a055"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e7377acf5f714d5845671a43d635d06f", "guid": "bfdfe7dc352907fc980b868725387e9842aab6e6820437225615089c83902796"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c5a85dceb4cf05ba97f3987baacec018", "guid": "bfdfe7dc352907fc980b868725387e9857aea391777027b04b5707666b521569"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c64e66b8a7ef25ed36318d291178ec67", "guid": "bfdfe7dc352907fc980b868725387e9881ead7d5ee06c80b6d2e153309173273"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9840e3e0401d9805d6180cef1fdb3d619c", "guid": "bfdfe7dc352907fc980b868725387e98eaaa226cb9428e050cb7f41b2f6c2bd1"}], "guid": "bfdfe7dc352907fc980b868725387e981b2b4abaaa263ff11fa795c21ec7f9d3", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e9850046155a8d9eab9c34f139cf05eb31e"}], "guid": "bfdfe7dc352907fc980b868725387e9800faf1016cc785048ac6ced913c4cff8", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98fc5dfdf9349a221f5258db13a60fd150", "targetReference": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3"}], "guid": "bfdfe7dc352907fc980b868725387e98651b31e0272f628eba1fc39908fbac59", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e1aba8ff8dc833f2269ce0a7182533b3", "name": "geolocator_apple-geolocator_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e9821d372cc1e7c7587a12aeda843619e39", "name": "geolocator_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e986ff8f87e011522b1b6328c84d9533927", "name": "geolocator_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}