{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98ca7cc92fc5129628a48de39fdd7a4fbb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e981599af408fc1abde1ccd73005d868e16", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9853da74d043417a691f189a5b9021e550", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987e7355055f349cf3fe15030cd77cff0f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9853da74d043417a691f189a5b9021e550", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/cloud_firestore/cloud_firestore-prefix.pch", "INFOPLIST_FILE": "Target Support Files/cloud_firestore/cloud_firestore-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "13.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/cloud_firestore/cloud_firestore.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "cloud_firestore", "PRODUCT_NAME": "cloud_firestore", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98aed4c196729956c8a61d4629928cdad8", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9885ff132eb201a635044805280b1ae3d8", "guid": "bfdfe7dc352907fc980b868725387e98979a868ec6cb25a6822a4624f9dc5c78", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989b71571116154f2aaf9af49804e7d2d4", "guid": "bfdfe7dc352907fc980b868725387e98c3f8e55905a6827e138519b930425395", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858b156d60f2aa8ec02a254f99fde2358", "guid": "bfdfe7dc352907fc980b868725387e98e782344849fe4c8722154594a6e1cbb8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d514c70b81b0527f0a038a407a8ca448", "guid": "bfdfe7dc352907fc980b868725387e9895ea52cfd6dd2dea307e44eb54ea4a14", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e3d1c6e7f7ce410d0f15156c69e07117", "guid": "bfdfe7dc352907fc980b868725387e989fe3f019704508c244aa66f52d9e5bf7", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a8235ac5e17dff9a8b87c0541851e4e", "guid": "bfdfe7dc352907fc980b868725387e981b69877123bfc8f810160a900daf8815", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98535f6cbe14667ec9b33a0124b4a1f52c", "guid": "bfdfe7dc352907fc980b868725387e98e5aeba95ad0f9e6abfe494a032d1b339", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d415aa39c359708338600f031a40ed19", "guid": "bfdfe7dc352907fc980b868725387e98a0f55caea9bad6b2f0da32895d02b90c", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c3d423593b23cb880e1920e6776fe6dc", "guid": "bfdfe7dc352907fc980b868725387e988910bc20b78e8dc5ec989af2806b8a72", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818a1c3cc56963b93d81cd9854fd920f0", "guid": "bfdfe7dc352907fc980b868725387e9879486ccb322733ca6b6062dd9b58081d", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982632b46e64753c9f8967bd9aab3c6fe5", "guid": "bfdfe7dc352907fc980b868725387e9840fc6e6599f591f08d41bea0ab2ffed6", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98724d864f1e6e8b04a066075702ed10dc", "guid": "bfdfe7dc352907fc980b868725387e98dc1f5d86428a66a35eaaf4d36db7908d", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98043ad83bb16affa88f319aea01cbb1f8", "guid": "bfdfe7dc352907fc980b868725387e9856ceebefc36f0bf7aac33949b9dccd45", "headerVisibility": "private"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f91838dc0ab0c7693a3dfaf074fc2795", "guid": "bfdfe7dc352907fc980b868725387e98d15d98988149a6a36c525a3b770b1ece", "headerVisibility": "private"}], "guid": "bfdfe7dc352907fc980b868725387e98b88ef27fd18bb6e3876c3f43db5901ba", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9870e5719c9ceeb057ac789321ac219a90", "guid": "bfdfe7dc352907fc980b868725387e986c038aa933ee0897ebdb9dceea5baae6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98869e12c17a5fefaf48f3e71693c9fd31", "guid": "bfdfe7dc352907fc980b868725387e983cb4f32ac4ccf45a237fcd8296897951"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7cef05b2232bda1a90ccfe9ef7a7140", "guid": "bfdfe7dc352907fc980b868725387e987cf912adb696c6c4347048be4a373454"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9862f50745c6f247d67944e95b6274f5f2", "guid": "bfdfe7dc352907fc980b868725387e987826fc28b4c4d79318c3c8284e086487"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825bd8876c033c0a6706933f0b5c145e4", "guid": "bfdfe7dc352907fc980b868725387e980ebbaa173d722c8609a33e0741ae52d1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ffeda85831ca8f97ff8d607d4805021", "guid": "bfdfe7dc352907fc980b868725387e988cd0345f3e706e2dddcd414f89b6faaf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986baf2fb5dee5915885e42647562f974c", "guid": "bfdfe7dc352907fc980b868725387e98bcd45a4e4f6a1d8628ee07fe8f9107ee"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a14951827b753bc0251b912372b2fabd", "guid": "bfdfe7dc352907fc980b868725387e9862a8fa976bd6a6e0792e2d8f20df06f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f4f597f4ed0564677de1cf94b7434b1", "guid": "bfdfe7dc352907fc980b868725387e98167307be16eee9b45c9e95800b4c7d7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98741478e4c9b6bb8508b24c968fcefff3", "guid": "bfdfe7dc352907fc980b868725387e987694e9b43605759b4ff239e298710a55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853071af707e723d902f1217518ddadec", "guid": "bfdfe7dc352907fc980b868725387e9814d364add039c25ea5294f32ad39deff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d185b7b79e45cd96d48b59b4bbe5e2e2", "guid": "bfdfe7dc352907fc980b868725387e98eeba9d66c9529a2b22863136396e41eb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a1b2211273c288e9483f88697857837", "guid": "bfdfe7dc352907fc980b868725387e98a5fdea3f060f936e4fdf444333a88971"}], "guid": "bfdfe7dc352907fc980b868725387e9835f4c8f61e73511fc560fa84bf693430", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98e00ce0119849653fc00c7474bcc623b4", "guid": "bfdfe7dc352907fc980b868725387e988aaf844137aefd0f7dadaa75c6732ac7"}], "guid": "bfdfe7dc352907fc980b868725387e985af968884e986bae48f258bcd28bb9de", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e980d2794352e93f0efd04427d324195dea", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e98d57b8bce60a0f11113f4cff532db68d3", "name": "Firebase"}, {"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987f74324bfc5c78140e34d510e26e00c1", "name": "firebase_core"}], "guid": "bfdfe7dc352907fc980b868725387e989840e8244cb75f43b3efe8cd6dec5ec5", "name": "cloud_firestore", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98321793542cff8793cba84baa893d5044", "name": "cloud_firestore.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}