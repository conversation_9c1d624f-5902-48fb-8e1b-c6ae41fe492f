import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class UserProvider with ChangeNotifier {
  User? _user;
  String? _profileImageUrl;
  String? _name;

  User? get user => _user;
  String? get profileImageUrl => _profileImageUrl;
  String? get name => _name;

  UserProvider() {
    FirebaseAuth.instance.authStateChanges().listen((User? user) async {
      _user = user;
      if (user != null) {
        DocumentSnapshot userDoc = await FirebaseFirestore.instance.collection('users').doc(user.uid).get();
        _profileImageUrl = (userDoc.data() as Map<String, dynamic>)['profileImageUrl'];
        _name = (userDoc.data() as Map<String, dynamic>)['name'];
      } else {
        _profileImageUrl = null;
        _name = null;
      }
      notifyListeners();
    });
  }
}