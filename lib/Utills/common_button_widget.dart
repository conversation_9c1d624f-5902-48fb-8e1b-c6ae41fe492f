import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CommonButtonWidget {
  static button({onTap, text, buttonColor}) {
    return MaterialButton(
      onPressed: onTap,
      height: 45,
      minWidth: Get.width,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      color: buttonColor,
      child: CommonTextWidget.PoppinsMedium(
        fontSize: 15,
        text: text,
        color: white,
      ),
    );
  }
}
