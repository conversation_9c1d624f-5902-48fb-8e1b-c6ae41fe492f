import 'package:fastyorderapp/Constants/font_family.dart';
import 'package:flutter/material.dart';

class CommonTextWidget {
  static Widget PoppinsBold(
      {String? text, Color? color, double? fontSize, TextAlign? textAlign}) {
    return Text(
      text!,
      textAlign: textAlign,
      style: TextStyle(
        color: color,
        fontSize: fontSize,
        fontFamily: FontFamily.PoppinsBold,
      ),
    );
  }

  static Widget PoppinsRegular(
      {String? text, Color? color, double? fontSize, TextAlign? textAlign}) {
    return Text(
      text!,
      textAlign: textAlign,
      style: TextStyle(
        color: color,
        fontSize: fontSize,
        fontFamily: FontFamily.PoppinsRegular,
      ),
    );
  }

  static Widget PoppinsSemiBold(
      {String? text, Color? color, double? fontSize, TextAlign? textAlign}) {
    return Text(
      text!,
      textAlign: textAlign,
      style: TextStyle(
        color: color,
        fontSize: fontSize,
        fontFamily: FontFamily.PoppinsSemiBold,
      ),
    );
  }

  static Widget PoppinsMedium(
      {String? text, Color? color, double? fontSize, TextAlign? textAlign}) {
    return Text(
      text!,
      textAlign: textAlign,
      style: TextStyle(
        color: color,
        fontSize: fontSize,
        fontFamily: FontFamily.PoppinsMedium,
      ),
    );
  }

  static Widget PoppinsLight(
      {String? text, Color? color, double? fontSize, TextAlign? textAlign}) {
    return Text(
      text!,
      textAlign: textAlign,
      style: TextStyle(
        color: color,
        fontSize: fontSize,
        fontFamily: FontFamily.PoppinsLight,
      ),
    );
  }
}