import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/font_family.dart';
import 'package:flutter/material.dart';

class CommonTextFieldWidget {
  static TextFormField1({
    prefixIcon,
    controller,
    keyboardType,
    hintText,
    enabled,
  }) {
    return TextFormField(
      keyboardType: TextInputType.number,
      cursorColor: black2D2,
      controller: controller,
      enabled: enabled ?? false,
      style: TextStyle(
        color: black2D2,
        fontSize: 14,
        fontFamily: FontFamily.PoppinsRegular,
      ),
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: TextStyle(
          color: grey9C9,
          fontSize: 14,
          fontFamily: FontFamily.PoppinsRegular,
        ),
        prefixIcon: prefixIcon,
        filled: true,
        fillColor: whiteF2F,
        contentPadding: EdgeInsets.zero,
        disabledBorder:  OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: whiteF2F, width: 0)),
        border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: whiteF2F, width: 0)),
        focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: whiteF2F, width: 0)),
        enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: whiteF2F, width: 0)),
        errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: BorderSide(color: whiteF2F, width: 0)),
      ),
    );
  }

  static TextFormField2({
    controller,
    keyboardType,
    hintText,
  }) {
    return TextFormField(
      keyboardType: TextInputType.number,
      cursorColor: black2D2,
      controller: controller,
      style: TextStyle(
        color: black2D2,
        fontSize: 14,
        fontFamily: FontFamily.PoppinsRegular,
      ),
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: TextStyle(
          color: grey909,
          fontSize: 14,
          fontFamily: FontFamily.PoppinsRegular,
        ),
        filled: true,
        fillColor: white,
        disabledBorder:  OutlineInputBorder(
            borderRadius: BorderRadius.circular(5),
            borderSide: BorderSide(color: greyD4D, width: 1)),
        border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5),
            borderSide: BorderSide(color: greyD4D, width: 1)),
        focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5),
            borderSide: BorderSide(color: greyD4D, width: 1)),
        enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5),
            borderSide: BorderSide(color: greyD4D, width: 1)),
        errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(5),
            borderSide: BorderSide(color: greyD4D, width: 1)),
      ),
    );
  }

  static TextFormField3({
    controller,
    keyboardType,
    hintText,
  }) {
    return TextFormField(
      keyboardType: keyboardType,
      cursorColor: black2D2,
      controller: controller,
      style: TextStyle(
        color: black2D2,
        fontSize: 14,
        fontFamily: FontFamily.PoppinsRegular,
      ),
      decoration: InputDecoration(
        hintText: hintText,
        hintStyle: TextStyle(
          color: grey818,
          fontSize: 16,
          fontFamily: FontFamily.PoppinsSemiBold,
        ),
        filled: true,
        fillColor: white,
        contentPadding: EdgeInsets.zero,
        disabledBorder:  UnderlineInputBorder(
            borderRadius: BorderRadius.circular(0),
            borderSide: BorderSide(color: greyE2E, width: 1)),
        border: UnderlineInputBorder(
            borderRadius: BorderRadius.circular(0),
            borderSide: BorderSide(color: greyE2E, width: 1)),
        focusedBorder: UnderlineInputBorder(
            borderRadius: BorderRadius.circular(0),
            borderSide: BorderSide(color: greyE2E, width: 1)),
        enabledBorder: UnderlineInputBorder(
            borderRadius: BorderRadius.circular(0),
            borderSide: BorderSide(color: greyE2E, width: 1)),
        errorBorder: UnderlineInputBorder(
            borderRadius: BorderRadius.circular(0),
            borderSide: BorderSide(color: greyE2E, width: 1)),
      ),
    );
  }
}
