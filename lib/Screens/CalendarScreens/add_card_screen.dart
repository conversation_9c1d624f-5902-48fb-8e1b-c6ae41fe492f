import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Screens/CalendarScreens/payment_successful_screen.dart';
import 'package:fastyorderapp/Utills/common_button_widget.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/Utills/common_textfield_widget.dart';
import 'package:fastyorderapp/main.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AddCardScreen extends StatelessWidget {
  AddCardScreen({Key? key}) : super(key: key);

  final TextEditingController cardNameController = TextEditingController();
  final TextEditingController cardNumberController = TextEditingController();
  final TextEditingController cardMmController = TextEditingController();
  final TextEditingController cardYyController = TextEditingController();
  final TextEditingController cardCvvController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      appBar: AppBar(
        backgroundColor: white,
        automaticallyImplyLeading: false,
        centerTitle: true,
        elevation: 0,
        leading: Padding(
          padding: EdgeInsets.only(left: 20),
          child: InkWell(
            onTap: () {
              Get.back();
            },
            child: Icon(
              Icons.arrow_back,
              color: black2D2,
              size: 24,
            ),
          ),
        ),
        title: CommonTextWidget.PoppinsMedium(
          text: "Add Card Details",
          fontSize: 18,
          color: black2D2,
        ),
      ),
      body: Stack(
        children: [
          Divider(
            thickness: 3,
            color: black.withOpacity(0.02),
          ),
          ScrollConfiguration(
            behavior: MyBehavior(),
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.only(top: 20, left: 25, right: 25),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonTextWidget.PoppinsMedium(
                      text: "Payment Options",
                      fontSize: 16,
                      color: black2D2,
                    ),
                    SizedBox(height: 15),
                    CommonTextFieldWidget.TextFormField2(
                      hintText: "Name on Card",
                      keyboardType: TextInputType.text,
                      controller: cardNameController,
                    ),
                    SizedBox(height: 15),
                    CommonTextFieldWidget.TextFormField2(
                      hintText: "Card Number",
                      keyboardType: TextInputType.number,
                      controller: cardNumberController,
                    ),
                    SizedBox(height: 15),
                    Row(
                      children: [
                        Expanded(
                          child: CommonTextFieldWidget.TextFormField2(
                            hintText: "MM",
                            keyboardType: TextInputType.number,
                            controller: cardMmController,
                          ),
                        ),
                        SizedBox(width: 12),
                        Expanded(
                          child: CommonTextFieldWidget.TextFormField2(
                            hintText: "YY",
                            keyboardType: TextInputType.number,
                            controller: cardYyController,
                          ),
                        ),
                        SizedBox(width: 12),
                        Expanded(
                          child: CommonTextFieldWidget.TextFormField2(
                            hintText: "CVV",
                            keyboardType: TextInputType.number,
                            controller: cardCvvController,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 22),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonTextWidget.PoppinsMedium(
                          text: "Sub Total",
                          fontSize: 16,
                          color: black2D2,
                        ),
                        CommonTextWidget.PoppinsMedium(
                          text: "₹ 1190",
                          fontSize: 16,
                          color: black2D2,
                        ),
                      ],
                    ),
                    SizedBox(height: 15),
                    Divider(
                      thickness: 1,
                      color: greyE0E,
                    ),
                    SizedBox(height: 15),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonTextWidget.PoppinsRegular(
                          text: "Paneer Nawabi",
                          fontSize: 14,
                          color: black2D2,
                        ),
                        CommonTextWidget.PoppinsRegular(
                          text: "₹300",
                          fontSize: 14,
                          color: black2D2,
                        ),
                      ],
                    ),
                    SizedBox(height: 10),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonTextWidget.PoppinsRegular(
                          text: "Dum Nihari Gosht (1/2 Kg)",
                          fontSize: 14,
                          color: black2D2,
                        ),
                        CommonTextWidget.PoppinsRegular(
                          text: "₹645",
                          fontSize: 14,
                          color: black2D2,
                        ),
                      ],
                    ),
                    SizedBox(height: 10),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonTextWidget.PoppinsRegular(
                          text: "Chicken Haleem (250 Gms)",
                          fontSize: 14,
                          color: black2D2,
                        ),
                        CommonTextWidget.PoppinsRegular(
                          text: "₹245",
                          fontSize: 14,
                          color: black2D2,
                        ),
                      ],
                    ),
                    SizedBox(height: 15),
                    Divider(
                      thickness: 1,
                      color: greyE0E,
                    ),
                    SizedBox(height: 15),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonTextWidget.PoppinsMedium(
                          text: "Total (incl. GST)",
                          fontSize: 16,
                          color: black2D2,
                        ),
                        CommonTextWidget.PoppinsMedium(
                          text: "₹ 1190",
                          fontSize: 16,
                          color: black2D2,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              decoration: BoxDecoration(
                color: white,
                boxShadow: [
                  BoxShadow(
                    color: black.withOpacity(0.08),
                    spreadRadius: 0,
                    offset: Offset(0, -4),
                    blurRadius: 16,
                  ),
                ],
              ),
              child: Padding(
                padding:
                    EdgeInsets.only(top: 15, bottom: 35, left: 25, right: 25),
                child: CommonButtonWidget.button(
                  text: "Payment",
                  buttonColor: yellowF9B,
                  onTap: () {
                    Get.to(() => PaymentSuccessFulScreen());
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
