import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Utills/common_button_widget.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../PaymentScreen/credit_card_screen.dart';

class PayBillScreen extends StatelessWidget {
  const PayBillScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      appBar: AppBar(
        backgroundColor: white,
        automaticallyImplyLeading: false,
        elevation: 0,
        leading: Padding(
          padding: EdgeInsets.only(left: 20),
          child: InkWell(
            onTap: () {
              Get.back();
            },
            child: Icon(
              Icons.arrow_back,
              color: black2D2,
              size: 24,
            ),
          ),
        ),
        title: CommonTextWidget.PoppinsMedium(
          text: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Ödeyin",
          fontSize: 18,
          color: black2D2,
        ),
        actions: [
          SizedBox(width: 30),

        ],
      ),
      body: Column(
        children: [
          Divider(
            thickness: 3,
            color: black.withOpacity(0.02),
          ),
          SizedBox(height: 50),
          CommonTextWidget.PoppinsMedium(
            text: "Sümerli Hub",
            fontSize: 20,
            color: black2D2,
          ),
          SizedBox(height: 5),
          CommonTextWidget.PoppinsRegular(
            text: "Etiler, İstanbul",
            fontSize: 14,
            color: black616,
          ),
          SizedBox(height: 15),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 65),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonTextWidget.PoppinsRegular(
                  text: "\u2022",
                  fontSize: 14,
                  color: black616,
                ),
                CommonTextWidget.PoppinsRegular(
                  text: "22 Tem’24",
                  fontSize: 14,
                  color: black616,
                ),
                CommonTextWidget.PoppinsRegular(
                  text: "\u2022",
                  fontSize: 14,
                  color: black616,
                ),
                CommonTextWidget.PoppinsRegular(
                  text: "12:30",
                  fontSize: 14,
                  color: black616,
                ),
                CommonTextWidget.PoppinsRegular(
                  text: "\u2022",
                  fontSize: 14,
                  color: black616,
                ),
                CommonTextWidget.PoppinsRegular(
                  text: "2 Kişi",
                  fontSize: 14,
                  color: black616,
                ),
              ],
            ),
          ),
          SizedBox(height: 40),
          CommonTextWidget.PoppinsMedium(
            text: "₺1.200",
            fontSize: 40,
            color: black2D2,
          ),
          Spacer(),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25),
            child: CommonButtonWidget.button(
              buttonColor: yellowF9B,
              onTap: () {
                Get.to(() => PaymentScreen());
              },
              text: "Ödemeye Geç",
            ),
          ),
          SizedBox(height: 50),
        ],
      ),
    );
  }
}
