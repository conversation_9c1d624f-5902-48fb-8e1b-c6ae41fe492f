import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Utills/common_button_widget.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class CouponScreen extends StatelessWidget {
  const CouponScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 20, bottom: 150),
      child: Container(
        width: Get.width,
        decoration: BoxDecoration(
          color: white,
          boxShadow: [
            BoxShadow(
              color: black.withOpacity(0.08),
              spreadRadius: 0,
              offset: Offset(0, 0),
              blurRadius: 16,
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SvgPicture.asset(Images.searchAdobeExpress),
            SizedBox(height: 20),
            CommonTextWidget.PoppinsRegular(
              text: "Aktif kupon bulunmamaktadır.",
              fontSize: 16,
              color: black2D2,
            ),
            SizedBox(height: 20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 100),
              child: CommonButtonWidget.button(
                text: "Şimdi Satın Al",
                onTap: () {},
                buttonColor: yellowF9B,
              ),
            ),
          ],
        ),
      ),
    );
  }
}