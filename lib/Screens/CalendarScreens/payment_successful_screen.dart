import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Controllers/navigation_controller.dart';
import 'package:fastyorderapp/Screens/NavigationSCreen/navigation_screen.dart';
import 'package:fastyorderapp/Utills/common_button_widget.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PaymentSuccessFulScreen extends StatelessWidget {
  PaymentSuccessFulScreen({Key? key}) : super(key: key);

  final NavigationController navigationController =
  Get.put(NavigationController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      body: Center(
        child: Column(
          children: [
            SizedBox(height: 165),
            Image.asset(Images.successImage, height: 100, width: 100),
            SizedBox(height: 20),
            CommonTextWidget.PoppinsMedium(
              text: "Payment Successful",
              fontSize: 20,
              color: black2D2,
            ),
            SizedBox(height: 10),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 45),
              child: CommonTextWidget.PoppinsLight(
                text: "Your payment has been processed! "
                    "Details of transaction are included below",
                fontSize: 14,
                color: black616,
                textAlign: TextAlign.center,
              ),
            ),
            Spacer(),
            Padding(
              padding: EdgeInsets.only(bottom: 50, left: 25, right: 25),
              child: CommonButtonWidget.button(
                text: "Back Home",
                onTap: () {
                  navigationController.pageIndex.value = 0;
                  Get.off(() => NavigationScreen());
                },
                buttonColor: yellowF9B,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
