import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class ReferAndEarnScreen extends StatelessWidget {
  const ReferAndEarnScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      body: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            height: 450,
            width: Get.width,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(Images.referAndEarnImage),
                fit: BoxFit.cover,
              ),
            ),
            child: Padding(
              padding:
              EdgeInsets.only(left: 25, right: 25, bottom: 25, top: 50),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: Icon(
                      Icons.arrow_back,
                      color: white,
                      size: 24,
                    ),
                  ),
                  SizedBox(height: 15),
                  Center(
                    child: CommonTextWidget.PoppinsSemiBold(
                      text: "Bir yemek kuponu veya 100₺ sizi \nbekliyor",
                      fontSize: 16,
                      color: white,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  SizedBox(height: 15),
                  Center(
                    child: CommonTextWidget.PoppinsRegular(
                      text: "Arkadaşınızı Fasty Order’a davet ettiğinizde ve üye olduklarında, siz ve arkadaşınız minimum 100₺'lik yemek kuponu kazanırsınız.",
                      fontSize: 16,
                      color: white,
                      textAlign: TextAlign.center,
                    ),
                  ),


                ],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20),
            child: DottedBorder(
              borderType: BorderType.RRect,
              radius: Radius.circular(4),
              dashPattern: [10, 10],
              color: grey868,
              strokeWidth: 1,
              padding: EdgeInsets.all(6),
              child: ClipRRect(
                borderRadius: BorderRadius.all(Radius.circular(4)),
                child: Container(
                  width: Get.width,
                  color: white,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20, vertical: 15),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonTextWidget.PoppinsMedium(
                          text: "AVHE81748",
                          fontSize: 16,
                          color: black2D2,
                        ),
                        InkWell(
                          onTap: () {
                            Clipboard.setData(ClipboardData(text: "AVHE81748"));
                            Get.snackbar("Başarılı", "Kod kopyalandı", snackPosition: SnackPosition.BOTTOM);
                          },
                          child: SvgPicture.asset(Images.copyIcon),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          Column(
            children: [
              CommonTextWidget.PoppinsMedium(
                text: "Paylaş",
                fontSize: 16,
                color: black2D2,
              ),
              SizedBox(height: 20),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 48),
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Image.asset(Images.twitterImage, height: 65, width: 65),
                      Image.asset(Images.instaGramIcon, height: 65, width: 65),
                      Image.asset(Images.whatsaapImage, height: 65, width: 65),
                      Image.asset(Images.faceBookImage, height: 65, width: 65),
                    ]),
              ),
            ],
          ),
          CommonTextWidget.PoppinsMedium(
            text: "Davet Geçmişi",
            fontSize: 16,
            color: yellowF9B,
          ),
          SizedBox(height: 20),
        ],
      ),
    );
  }
}
