import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Screens/CalendarScreens/pay_bill_screen.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/main.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class BookingScreen extends StatelessWidget {
  BookingScreen({Key? key}) : super(key: key);

  final upComingBookingList = [
    {
      "image": Images.ishaara,
      "text1": "Nusr-Et",
      "text2": "Perşembe, 22 Eylül saat 12:30",
      "text3": "Rezervasyon No: 857718418",
    },
    {
      "image": Images.rudeLoungeImage,
      "text1": "Çarşıbaşı",
      "text2": "Perşembe, 29 Eylül saat 12:30",
      "text3": "Rezervasyon No: 644141745",
    },
  ];

  final pastBookingList = [
    {
      "image": Images.ishaara,
      "text": "Nusr-Et",
    },
    {
      "image": Images.rudeLoungeImage,
      "text": "Çar<PERSON>ıbaşı",
    },
  ];

  @override
  Widget build(BuildContext context) {
    return ScrollConfiguration(
      behavior: MyBehavior(),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(vertical: 20, horizontal: 25),
              child: CommonTextWidget.PoppinsMedium(
                text: "Yaklaşan Rezervasyonlar ve Siparişler",
                fontSize: 16,
                color: black2D2,
              ),
            ),
            ListView.builder(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemCount: upComingBookingList.length,
              physics: NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) => Padding(
                padding: EdgeInsets.only(bottom: 15),
                child: InkWell(
                  onTap: () {
                    Get.to(() => PayBillScreen());
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: white,
                      boxShadow: [
                        BoxShadow(
                          color: black.withOpacity(0.08),
                          blurRadius: 16,
                          offset: Offset(0, 0),
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 15, vertical: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    height: 100,
                                    width: 90,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      image: DecorationImage(
                                        image: AssetImage(
                                          upComingBookingList[index]["image"].toString(),
                                        ),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      CommonTextWidget.PoppinsSemiBold(
                                        text: upComingBookingList[index]["text1"],
                                        fontSize: 18,
                                        color: black2D2,
                                      ),
                                      CommonTextWidget.PoppinsRegular(
                                        text: upComingBookingList[index]["text2"],
                                        fontSize: 12,
                                        color: grey969,
                                      ),
                                      SizedBox(height: 5),
                                      CommonTextWidget.PoppinsRegular(
                                        text: "2 Kişi",
                                        fontSize: 12,
                                        color: grey969,
                                      ),
                                      SizedBox(height: 14),
                                      CommonTextWidget.PoppinsRegular(
                                        text: upComingBookingList[index]["text3"],
                                        fontSize: 12,
                                        color: black2D2,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              CommonTextWidget.PoppinsMedium(
                                text: "Onaylandı",
                                fontSize: 12,
                                color: green009,
                              ),
                            ],
                          ),
                          SizedBox(height: 28),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              CommonTextWidget.PoppinsMedium(
                                text: "Uygulamada öde ve %30'a kadar indirim kazan",
                                fontSize: 11,
                                color: grey707,
                              ),
                              MaterialButton(
                                onPressed: () {
                                  Get.to(() => PayBillScreen());
                                },
                                height: 35,
                                minWidth: 85,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                color: yellowF9B,
                                child: CommonTextWidget.PoppinsSemiBold(
                                  fontSize: 12,
                                  text: "Hesap Öde",
                                  color: white,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(bottom: 20, left: 25, right: 25),
              child: CommonTextWidget.PoppinsMedium(
                text: "Geçmiş Rezervasyonlar ve Siparişler",
                fontSize: 16,
                color: black2D2,
              ),
            ),
            ListView.builder(
              padding: EdgeInsets.zero,
              shrinkWrap: true,
              itemCount: pastBookingList.length,
              physics: NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) => Padding(
                padding: EdgeInsets.only(bottom: 15),
                child: Container(
                  decoration: BoxDecoration(
                    color: white,
                    boxShadow: [
                      BoxShadow(
                        color: black.withOpacity(0.08),
                        blurRadius: 16,
                        offset: Offset(0, 0),
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 15, vertical: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Container(
                                  height: 100,
                                  width: 90,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    image: DecorationImage(
                                      image: AssetImage(
                                        pastBookingList[index]["image"].toString(),
                                      ),
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    CommonTextWidget.PoppinsSemiBold(
                                      text: pastBookingList[index]["text"],
                                      fontSize: 18,
                                      color: black2D2,
                                    ),
                                    CommonTextWidget.PoppinsRegular(
                                      text: "Perşembe, 22 Eylül saat 12:30",
                                      fontSize: 12,
                                      color: grey969,
                                    ),
                                    SizedBox(height: 5),
                                    CommonTextWidget.PoppinsRegular(
                                      text: "2 Kişi",
                                      fontSize: 12,
                                      color: grey969,
                                    ),
                                    SizedBox(height: 14),
                                    CommonTextWidget.PoppinsRegular(
                                      text: "Rezervasyon No: 857718418",
                                      fontSize: 12,
                                      color: black2D2,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            CommonTextWidget.PoppinsMedium(
                              text: "Ödendi",
                              fontSize: 12,
                              color: green009,
                            ),
                          ],
                        ),
                        SizedBox(height: 20),
                        CommonTextWidget.PoppinsSemiBold(
                          text: "Toplam Hesap",
                          fontSize: 14,
                          color: black2D2,
                        ),
                        SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CommonTextWidget.PoppinsRegular(
                              text: "Kebap",
                              fontSize: 12,
                              color: black2D2,
                            ),
                            CommonTextWidget.PoppinsRegular(
                              text: "₺300",
                              fontSize: 12,
                              color: black2D2,
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CommonTextWidget.PoppinsRegular(
                              text: "Adana Kebap",
                              fontSize: 12,
                              color: black2D2,
                            ),
                            CommonTextWidget.PoppinsRegular(
                              text: "₺250",
                              fontSize: 12,
                              color: black2D2,
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CommonTextWidget.PoppinsRegular(
                              text: "Lahmacun",
                              fontSize: 12,
                              color: black2D2,
                            ),
                            CommonTextWidget.PoppinsRegular(
                              text: "₺75",
                              fontSize: 12,
                              color: black2D2,
                            ),
                          ],
                        ),
                        SizedBox(height: 15),
                        Divider(color: greyE0E, thickness: 1),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CommonTextWidget.PoppinsSemiBold(
                              text: "Toplam Hesap",
                              fontSize: 14,
                              color: black2D2,
                            ),
                            CommonTextWidget.PoppinsSemiBold(
                              text: "₺625",
                              fontSize: 14,
                              color: black2D2,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            SizedBox(height: 100),
          ],
        ),
      ),
    );
  }
}
