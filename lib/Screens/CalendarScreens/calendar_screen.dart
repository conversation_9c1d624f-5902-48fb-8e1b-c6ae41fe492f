import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/font_family.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Controllers/calendar_tab_controller.dart';
import 'package:fastyorderapp/Screens/CalendarScreens/booking_screen.dart';
import 'package:fastyorderapp/Screens/CalendarScreens/coupon_screen.dart';
import 'package:fastyorderapp/Screens/CalendarScreens/refer_and_earn_screen.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class CalendarScreen extends StatelessWidget {
  CalendarScreen({Key? key}) : super(key: key);

  final CalendarTabController calendarTabController =
      Get.put(CalendarTabController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      appBar: AppBar(
        backgroundColor: white,
        automaticallyImplyLeading: false,
        centerTitle: true,
        elevation: 0,
        title: CommonTextWidget.PoppinsMedium(
          text: "Rezervasyonlarım",
          fontSize: 18,
          color: black2D2,
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Divider(
            thickness: 3,
            color: black.withOpacity(0.02),
          ),
          Padding(
            padding: EdgeInsets.only(left: 25, right: 25, top: 15, bottom: 25),
            child: Container(
              width: Get.width,
              decoration: BoxDecoration(
                color: yellowFFF1,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 20, vertical: 18),
                child: Row(
                  children: [
                    SvgPicture.asset(Images.bookingImage),
                    SizedBox(width: 15),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CommonTextWidget.PoppinsMedium(
                            text: "300₺ değerinde yemek kuponu seni bekliyor",
                            fontSize: 14,
                            color: black2D2,
                          ),
                          SizedBox(height: 5),
                          InkWell(
                            onTap: () {
                              Get.to(() => ReferAndEarnScreen());
                            },
                            child: Text(
                              "Davet et ve kazan",
                              style: TextStyle(
                                decoration: TextDecoration.underline,
                                color: black2D2,
                                fontSize: 12,
                                fontFamily: FontFamily.PoppinsRegular,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          Container(
            height: 40,
            width: Get.width,
            color: white,
            child: TabBar(
              tabs: calendarTabController.myTabs,
              unselectedLabelColor: grey9E9,
              labelStyle: TextStyle(fontFamily: "PoppinsRegular", fontSize: 18),
              unselectedLabelStyle:
                  TextStyle(fontFamily: "PoppinsRegular", fontSize: 18),
              labelColor: black2D2,
              controller: calendarTabController.controller,
              indicatorColor: yellowF9B,
              indicatorWeight: 1.25,
            ),
          ),
          Expanded(
            child: TabBarView(
              controller: calendarTabController.controller,
              children: [
                BookingScreen(),
                CouponScreen(),
              ],
            ),
          )
        ],
      ),
    );
  }
}