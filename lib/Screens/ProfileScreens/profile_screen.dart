import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Screens/ProfileScreens/about_us_screen.dart';
import 'package:fastyorderapp/Screens/ProfileScreens/change_profile_screen.dart';
import 'package:fastyorderapp/Screens/ProfileScreens/privacy_policy_screen.dart';
import 'package:fastyorderapp/Screens/ProfileScreens/terms_conditions_screen.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/providers/user_provider.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  Widget _buildProfileHeader(User? user, String? profileImageUrl, String? name, BuildContext context) {
    return Container(
      width: Get.width,
      color: yellowFFF1,
      padding: EdgeInsets.symmetric(horizontal: 25, vertical: 25),
      child: Row(
        children: [
          profileImageUrl != null
              ? Image.network(profileImageUrl, height: 60, width: 60)
              : Image.asset(Images.profileImage, height: 60, width: 60),
          SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CommonTextWidget.PoppinsMedium(
                text: name ?? "İsim Eklenmedi",
                fontSize: 14,
                color: black2D2,
              ),
              SizedBox(height: 3),
              CommonTextWidget.PoppinsRegular(
                text: user?.email ?? "",
                fontSize: 12,
                color: black616,
              ),
              CommonTextWidget.PoppinsRegular(
                text: user?.phoneNumber ?? "",
                fontSize: 12,
                color: black616,
              ),
            ],
          ),
          Expanded(
            child: InkWell(
              onTap: () {
                Get.to(() => ChangeProfileScreen());
              },
              child: Align(
                alignment: Alignment.centerRight,
                child: Image.asset(Images.editImage, height: 40, width: 40),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListItem(Map item, BuildContext context) {
    return InkWell(
      onTap: item["onTap"],
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 25, vertical: 15),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            item.containsKey("image")
                ? SvgPicture.asset(
                    item["image"],
                    height: 24,
                    width: 24,
                  )
                : Container(),
            SizedBox(width: 10),
            Expanded(
              child: CommonTextWidget.PoppinsRegular(
                text: item["text"],
                fontSize: 13,
                color: item["text"] == "Çıkış Yap" ? redFB4 : black2D2,
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: black2D2, size: 18),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    final user = userProvider.user;
    final profileImageUrl = userProvider.profileImageUrl;
    final name = userProvider.name;

    final List<Map> list1 = [

      {
        "text": "Yorumlarım",
        "onTap": () {
        //  Get.to(() => UserReviewScreen());
        },
      },
    ];

    final List<Map> list2 = [
      {
        "image": Images.signOut,
        "text": "Çıkış Yap",
        "onTap": () {
          Get.defaultDialog(
            backgroundColor: white,
            contentPadding: EdgeInsets.zero,
            title: "",
            titlePadding: EdgeInsets.zero,
            content: Padding(
              padding: EdgeInsets.symmetric(horizontal: 30),
              child: Column(
                children: [
                  Icon(Icons.logout, color: yellowF9B, size: 30),
                  SizedBox(height: 22),
                  CommonTextWidget.PoppinsRegular(
                    text: "Çıkış yapmak istediğinizden emin misiniz?",
                    color: black2D2,
                    fontSize: 14,
                  ),
                  SizedBox(height: 22),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            Get.back();
                          },
                          child: Container(
                            height: 40,
                            width: Get.width,
                            decoration: BoxDecoration(
                              border: Border.all(color: black2D2, width: 1),
                              color: white,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Center(
                              child: CommonTextWidget.PoppinsMedium(
                                text: "Hayır",
                                color: black2D2,
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 20),
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            FirebaseAuth.instance.signOut();
                          },
                          child: Container(
                            height: 40,
                            width: Get.width,
                            decoration: BoxDecoration(
                              color: yellowF9B,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Center(
                              child: CommonTextWidget.PoppinsMedium(
                                  text: "Evet",
                                  color: white,
                                  fontSize: 16),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      },
      {
        "image": Images.chatCircleDots,
        "text": "Canlı Destek",
        "onTap": () {},
      },
      {
        "image": Images.star,
        "text": "Puan Ver",
        "onTap": () {},
      },
      {
        "image": Images.circleWavyWarning,
        "text": "Hakkımızda",
        "onTap": () {
          Get.to(() => AboutUsScreen());
        },
      },
      {
        "image": Images.lock,
        "text": "Gizlilik Politikası",
        "onTap": () {
          Get.to(() => PrivacyPolicyScreen());
        },
      },
      {
        "image": Images.circleWavyQuestion,
        "text": "Şartlar ve Koşullar",
        "onTap": () {
          Get.to(() => TermsConditionsScreen());
        },
      },
    ];

    return Scaffold(
      backgroundColor: white,
      appBar: AppBar(
        backgroundColor: white,
        automaticallyImplyLeading: false,
        centerTitle: true,
        elevation: 0,
        title: CommonTextWidget.PoppinsMedium(
          text: "Profil",
          fontSize: 18,
          color: black2D2,
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildProfileHeader(user, profileImageUrl, name, context),
            ...list1.map((item) => _buildListItem(item, context)).toList(),
            ...list2.map((item) => _buildListItem(item, context)).toList(),
          ],
        ),
      ),
    );
  }
}