import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PrivacyPolicyScreen extends StatelessWidget {
  const PrivacyPolicyScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      appBar: AppBar(
        backgroundColor: white,
        automaticallyImplyLeading: false,
        centerTitle: true,
        elevation: 0,
        leading: Padding(
          padding: EdgeInsets.only(left: 20),
          child: InkWell(
            onTap: () {
              Get.back();
            },
            child: Icon(
              Icons.arrow_back,
              color: black2D2,
              size: 24,
            ),
          ),
        ),
        title: CommonTextWidget.PoppinsMedium(
          text: "Privacy Policy",
          fontSize: 18,
          color: black2D2,
        ),
      ),
      body: Column(
        children: [
          Divider(
            thickness: 3,
            color: black.withOpacity(0.02),
          ),
          Si<PERSON>Box(height: 25),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25),
            child:  CommonTextWidget.PoppinsRegular(
              text: "Lorem ipsum, or lipsum as it is sometimes known, is dummy text used in laying out print, graphic or web designs. The passage is attributed to an unknown Lorem ipsum, or lipsum as it is sometimes known, is dummy text used in laying out print, graphic or web designs. The passage is attributed to an unknown Lorem ipsum, or lipsum as it is sometimes known, is dummy text used in laying out print, graphic or web designs. The passage is attributed to an unknown Lorem ipsum, or lipsum as it is sometimes known, is dummy text used in laying out print, graphic or web designs. The passage is attributed to an unknown ",
              fontSize: 14,
              color: black2D2,
            ),
          ),
          SizedBox(height: 10),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25),
            child:  CommonTextWidget.PoppinsRegular(
              text: "Lorem ipsum, or lipsum as it is sometimes known, is dummy text used in laying out print, graphic or web designs. The passage is attributed to an unknown Lorem ipsum, or lipsum as it is sometimes known, is dummy text used in laying out print, graphic or web designs. The passage is attributed to an unknown Lorem ipsum, or lipsum as it is sometimes known, is dummy text used in laying out print, graphic or web designs. The passage is attributed to an unknown Lorem ipsum, or lipsum as it is sometimes known, is dummy text used in laying out print, graphic or web designs. The passage is attributed to an unknown ",
              fontSize: 14,
              color: black2D2,
            ),
          ),
        ],
      ),
    );
  }
}
