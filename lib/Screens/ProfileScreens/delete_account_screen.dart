import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Utills/common_button_widget.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/main.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DeleteAccountScreen extends StatelessWidget {
  DeleteAccountScreen({Key? key}) : super(key: key);

  final List deleteAccountList = [
    "Fasty Order'ı artık kullanmak istemiyorum",
    "Başka bir hesap kullanıyorum",
    "<PERSON><PERSON><PERSON><PERSON>ğim hakkında endişeliyim",
    "Çok fazla bildirim gönderiyorsunuz",
    "<PERSON><PERSON><PERSON>",
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.all(25),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap:(){
                  Get.back();
                },
                child: Icon(Icons.arrow_back, color: black2D2, size: 25),
              ),
              CommonTextWidget.PoppinsSemiBold(
                text: "Hesabı Sil",
                color: black2D2,
                fontSize: 16,
              ),
              Icon(Icons.arrow_back, color: white),
            ],
          ),
        ),
        Divider(color: greyE0E, thickness: 1),
        Expanded(
          child: ScrollConfiguration(
            behavior: MyBehavior(),
            child: ListView.builder(
              padding: EdgeInsets.only(top: 22),
              shrinkWrap: true,
              itemCount: deleteAccountList.length,
              itemBuilder: (context, index) => InkWell(
                onTap: () {
                  Get.bottomSheet(
                    Column(
                      children: [
                        Align(
                          alignment: Alignment.topRight,
                          child: InkWell(
                            onTap: () {
                              Get.back();
                            },
                            child: Padding(
                              padding: EdgeInsets.only(
                                  right: 25, top: 22, bottom: 15),
                              child:
                              Icon(Icons.close, color: black2D2, size: 20),
                            ),
                          ),
                        ),
                        CommonTextWidget.PoppinsMedium(
                          fontSize: 14,
                          color: black2D2,
                          text: "Hesabınızı silmek üzeresiniz",
                        ),
                        SizedBox(height: 10),
                        CommonTextWidget.PoppinsRegular(
                          fontSize: 12,
                          color: black616,
                          text:
                          "Profiliniz önce devre dışı bırakılacak ve 30 gün içinde "
                              "silinecektir. Hesabınız silindikten sonra "
                              "bilgileriniz kurtarılamaz.",
                          textAlign: TextAlign.center,
                        ),
                        Spacer(),
                        Padding(
                          padding: EdgeInsets.only(left: 25, right: 25, bottom: 50),
                          child: CommonButtonWidget.button(
                            text: "Hesabımı şimdi sil",
                            buttonColor: yellowF9B,
                            onTap: () {},
                          ),
                        ),
                      ],
                    ),
                    backgroundColor: white,
                  );
                },
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 25),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonTextWidget.PoppinsMedium(
                            fontSize: 14,
                            color: black2D2,
                            text: deleteAccountList[index],
                          ),
                          Icon(Icons.arrow_forward_ios,
                              color: black2D2, size: 14),
                        ],
                      ),
                    ),
                    SizedBox(height: 22),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 12),
                      child: Divider(thickness: 1, color: greyE0E),
                    ),
                    SizedBox(height: 22),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
