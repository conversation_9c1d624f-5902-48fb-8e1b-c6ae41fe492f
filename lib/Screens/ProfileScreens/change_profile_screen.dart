import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Screens/ProfileScreens/delete_account_screen.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/Utills/common_textfield_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

class ChangeProfileScreen extends StatefulWidget {
  const ChangeProfileScreen({Key? key}) : super(key: key);

  @override
  _ChangeProfileScreenState createState() => _ChangeProfileScreenState();
}

class _ChangeProfileScreenState extends State<ChangeProfileScreen> {
  final TextEditingController nameController = TextEditingController();
  final TextEditingController surnameController = TextEditingController();
  final TextEditingController mobileNumberController = TextEditingController();
  final TextEditingController birthdayController = TextEditingController();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseStorage _storage = FirebaseStorage.instance;

  String? _profileImageUrl;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    User? user = _auth.currentUser;
    if (user != null) {
      DocumentSnapshot userDoc =
          await _firestore.collection('users').doc(user.uid).get();
      if (userDoc.exists) {
        setState(() {
          nameController.text = userDoc['name'] ?? '';
          surnameController.text = userDoc['surname'] ?? '';
          mobileNumberController.text = user.phoneNumber ?? '';
          birthdayController.text = userDoc['birthday'] ?? '';
          _profileImageUrl = userDoc['profileImageUrl'];
        });
      }
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(1900),
      lastDate: DateTime(2100),
      locale: const Locale('tr', ''), // Set the locale to Turkish
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            primaryColor: yellowF9B, // Header background color
            hintColor: yellowF9B, // Header text color
            colorScheme: ColorScheme.light(primary: yellowF9B),
            buttonTheme: ButtonThemeData(textTheme: ButtonTextTheme.primary), // Button text color
          ),
          child: child!,
        );
      },
    );
    if (picked != null) {
      setState(() {
        birthdayController.text = "${picked.day}/${picked.month}/${picked.year}";
      });
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: source);

    if (pickedFile != null) {
      File imageFile = File(pickedFile.path);
      String fileName = 'profile_images/${_auth.currentUser!.uid}.png';
      try {
        await _storage.ref(fileName).putFile(imageFile);
        String downloadUrl = await _storage.ref(fileName).getDownloadURL();
        setState(() {
          _profileImageUrl = downloadUrl;
        });
        await _firestore.collection('users').doc(_auth.currentUser!.uid).update({
          'profileImageUrl': _profileImageUrl,
        });
      } catch (e) {
        Get.snackbar('Error', 'Failed to upload image: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      appBar: AppBar(
        backgroundColor: white,
        automaticallyImplyLeading: false,
        centerTitle: true,
        elevation: 0,
        leading: Padding(
          padding: EdgeInsets.only(left: 20),
          child: InkWell(
            onTap: () {
              Get.back();
            },
            child: Icon(
              Icons.arrow_back,
              color: black2D2,
              size: 24,
            ),
          ),
        ),
        title: CommonTextWidget.PoppinsMedium(
          text: "Profil",
          fontSize: 18,
          color: black2D2,
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Divider(
            thickness: 3,
            color: black.withOpacity(0.02),
          ),
          SizedBox(height: 25),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25),
            child: CommonTextWidget.PoppinsRegular(
              text: "Telefon Numarası (Değiştirilemez)",
              fontSize: 14,
              color: grey818,
            ),
          ),
          SizedBox(height: 15),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25),
            child: TextField(
              controller: mobileNumberController,
              keyboardType: TextInputType.number,
              readOnly: true,
              style: TextStyle(color: grey818),
              decoration: InputDecoration(
                hintText: "+90 5XX XXX XX XX",
                border: OutlineInputBorder(),
              ),
            ),
          ),
          SizedBox(height: 30),
          Center(
            child: _profileImageUrl == null
                ? Image.asset(Images.profileImage, height: 110, width: 110)
                : Image.network(_profileImageUrl!, height: 110, width: 110),
          ),
          SizedBox(height: 15),
          InkWell(
            onTap: () {
              Get.defaultDialog(
                backgroundColor: white,
                contentPadding: EdgeInsets.zero,
                title: "",
                titlePadding: EdgeInsets.zero,
                content: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonTextWidget.PoppinsMedium(
                            text: "Profil fotoğrafı ekle",
                            color: black2D2,
                            fontSize: 18,
                          ),
                          Icon(Icons.close, color: black2D2, size: 18),
                        ],
                      ),
                    ),
                    SizedBox(height: 18),
                    Divider(thickness: 1, color: greyE0E),
                    SizedBox(height: 20),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 22),
                      child: InkWell(
                        onTap: () {
                          _pickImage(ImageSource.camera);
                          Get.back();
                        },
                        child: CommonTextWidget.PoppinsRegular(
                          text: "Fotoğraf çek",
                          color: black2D2,
                          fontSize: 15,
                        ),
                      ),
                    ),
                    SizedBox(height: 25),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 22),
                      child: InkWell(
                        onTap: () {
                          _pickImage(ImageSource.gallery);
                          Get.back();
                        },
                        child: CommonTextWidget.PoppinsRegular(
                          text: "Kütüphaneden seç",
                          color: black2D2,
                          fontSize: 15,
                        ),
                      ),
                    ),
                    SizedBox(height: 25),
                  ],
                ),
              );
            },
            child: Center(
              child: CommonTextWidget.PoppinsMedium(
                text: "Profil fotoğrafını değiştir",
                fontSize: 14,
                color: yellowF9B,
              ),
            ),
          ),
          SizedBox(height: 30),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25),
            child: CommonTextWidget.PoppinsRegular(
              text: "Ad",
              fontSize: 14,
              color: black2D2,
            ),
          ),
          SizedBox(height: 15),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25),
            child: CommonTextFieldWidget.TextFormField3(
              controller: nameController,
              keyboardType: TextInputType.text,
              hintText: "Adınızı girin",
            ),
          ),
          SizedBox(height: 20),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25),
            child: CommonTextWidget.PoppinsRegular(
              text: "Soyad",
              fontSize: 14,
              color: black2D2,
            ),
          ),
          SizedBox(height: 15),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25),
            child: CommonTextFieldWidget.TextFormField3(
              controller: surnameController,
              keyboardType: TextInputType.text,
              hintText: "Soyadınızı girin",
            ),
          ),
          SizedBox(height: 20),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25),
            child: CommonTextWidget.PoppinsRegular(
              text: "Doğum Günü",
              fontSize: 14,
              color: black2D2,
            ),
          ),
          SizedBox(height: 15),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25),
            child: GestureDetector(
              onTap: () => _selectDate(context),
              child: AbsorbPointer(
                child: CommonTextFieldWidget.TextFormField3(
                  controller: birthdayController,
                  keyboardType: TextInputType.datetime,
                  hintText: "GG/AA/YYYY",
                ),
              ),
            ),
          ),
          SizedBox(height: 20),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25),
            child: InkWell(
              onTap: () {
                Get.bottomSheet(
                  DeleteAccountScreen(),
                  backgroundColor: white,
                );
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CommonTextWidget.PoppinsMedium(
                    text: "Hesabı Sil",
                    fontSize: 13,
                    color: redFB4,
                  ),
                  Icon(Icons.arrow_forward_ios, size: 14, color: black2D2),
                ],
              ),
            ),
          ),
          SizedBox(height: 20),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25),
            child: Divider(
              thickness: 1,
              color: greyE0E,
            ),
          ),
          Spacer(),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                MaterialButton(
                  onPressed: () {},
                  height: 38,
                  minWidth: 95,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  color: yellowF9B,
                  child: CommonTextWidget.PoppinsMedium(
                    fontSize: 15,
                    text: "İptal",
                    color: white,
                  ),
                ),
                MaterialButton(
                  onPressed: () async {
                    await _updateUserProfile();
                  },
                  height: 38,
                  minWidth: 95,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  color: yellowF9B,
                  child: CommonTextWidget.PoppinsMedium(
                    fontSize: 15,
                    text: "Kaydet",
                    color: white,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 50),
        ],
      ),
    );
  }

  Future<void> _updateUserProfile() async {
    User? user = _auth.currentUser;
    if (user != null) {
      try {
        await _firestore.collection('users').doc(user.uid).update({
          'name': nameController.text,
          'surname': surnameController.text,
          'birthday': birthdayController.text,
          'profileImageUrl': _profileImageUrl,
        });
        Get.snackbar('Başarılı', 'Profil başarıyla güncellendi');
      } catch (e) {
        Get.snackbar('Hata', 'Profil güncellenirken bir hata oluştu: $e');
      }
    } else {
      Get.snackbar('Hata', 'Giriş yapılmış kullanıcı yok');
    }
  }
}