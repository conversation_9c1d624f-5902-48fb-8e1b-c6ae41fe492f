
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';

import '../HomeScreen/firm_detail_screen.dart';

class FavoriteFirmsWidget extends StatelessWidget {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final String userId;

  FavoriteFirmsWidget({required this.userId});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<DocumentSnapshot>(
      stream: _firestore.collection('users').doc(userId).snapshots(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Center(child: Text('Bir hata oluştu'));
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(child: CircularProgressIndicator());
        }

        if (!snapshot.hasData || snapshot.data == null || snapshot.data!.data() == null) {
          return Center(child: Text('Favori mekan bulunamadı'));
        }

        final userData = snapshot.data!.data() as Map<String, dynamic>;
        final favoriteFirms = userData['favoritedFirms'] as List<dynamic>? ?? [];

        if (favoriteFirms.isEmpty) {
          return Center(child: Text('Favori mekan bulunamadı'));
        }

        return StreamBuilder<QuerySnapshot>(
          stream: _firestore.collection('firms').where(FieldPath.documentId, whereIn: favoriteFirms).snapshots(),
          builder: (context, firmSnapshot) {
            if (firmSnapshot.hasError) {
              return Center(child: Text('Bir hata oluştu'));
            }

            if (firmSnapshot.connectionState == ConnectionState.waiting) {
              return Center(child: CircularProgressIndicator());
            }

            return Padding(
              padding: EdgeInsets.only(top: 20, left: 25, right: 25),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonTextWidget.PoppinsMedium(
                    text: "Favori Mekanlar",
                    fontSize: 16,
                    color: black2D2,
                  ),
                  SizedBox(height: 15),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    itemCount: firmSnapshot.data!.docs.length,
                    padding: EdgeInsets.zero,
                    itemBuilder: (context, index) {
                      final doc = firmSnapshot.data!.docs[index];
                      final data = doc.data() as Map<String, dynamic>;

                      return Padding(
                        padding: EdgeInsets.only(bottom: 20),
                        child: InkWell(
                          onTap: () {
                            Get.to(() => FirmDetailScreen(firmId: doc.id));
                          },
                          child: Row(
                            children: [
                              Container(
                                height: 75,
                                width: 75,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(12),
                                  image: DecorationImage(
                                    image: NetworkImage(data['logo'] ?? ''),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              SizedBox(width: 12),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CommonTextWidget.PoppinsMedium(
                                    text: data['name'] ?? '',
                                    fontSize: 14,
                                    color: black2D2,
                                  ),
                                  CommonTextWidget.PoppinsLight(
                                    text: "${data['district'] ?? ''}, ${data['city'] ?? ''}",
                                    fontSize: 10,
                                    color: black2D2,
                                  ),
                                  CommonTextWidget.PoppinsMedium(
                                    text: "10% ",
                                    fontSize: 12,
                                    color: yellowF9B,
                                  ),
                                ],
                              ),
                              Expanded(
                                child: Align(
                                  alignment: Alignment.centerRight,
                                  child: Container(
                                    height: 18,
                                    width: 32,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(12),
                                      color: yellowF9B,
                                    ),
                                    child: Center(
                                      child: CommonTextWidget.PoppinsMedium(
                                        text: "4.4",
                                        fontSize: 12,
                                        color: white,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}