import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Utills/common_button_widget.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PaymentSummeryScreen extends StatelessWidget {
  const PaymentSummeryScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      appBar: AppBar(
        backgroundColor: white,
        automaticallyImplyLeading: false,
        centerTitle: true,
        elevation: 0,
        leading: Padding(
          padding: EdgeInsets.only(left: 20),
          child: InkWell(
            onTap: () {
              Get.back();
            },
            child: Icon(
              Icons.arrow_back,
              color: black2D2,
              size: 24,
            ),
          ),
        ),
        title: CommonTextWidget.PoppinsMedium(
          text: "Payment Summary",
          fontSize: 18,
          color: black2D2,
        ),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 25),
        child: Column(
          children: [
            Divider(
              thickness: 3,
              color: black.withOpacity(0.02),
            ),
            SizedBox(height: 25),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonTextWidget.PoppinsMedium(
                  text: "Sub Total (12 Months)",
                  fontSize: 16,
                  color: black2D2,
                ),
                CommonTextWidget.PoppinsMedium(
                  text: "₹ 2495",
                  fontSize: 16,
                  color: black2D2,
                ),
              ],
            ),
            SizedBox(height: 15),
            Divider(thickness: 1, color: greyE0E),
            SizedBox(height: 15),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonTextWidget.PoppinsRegular(
                  text: "Subtotal",
                  fontSize: 14,
                  color: black2D2,
                ),
                CommonTextWidget.PoppinsRegular(
                  text: "₹ 2495",
                  fontSize: 14,
                  color: black2D2,
                ),
              ],
            ),
            SizedBox(height: 12),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonTextWidget.PoppinsRegular(
                  text: "GST 18%",
                  fontSize: 14,
                  color: black2D2,
                ),
                CommonTextWidget.PoppinsRegular(
                  text: "₹ 449",
                  fontSize: 14,
                  color: black2D2,
                ),
              ],
            ),
            SizedBox(height: 15),
            Divider(thickness: 1, color: greyE0E),
            SizedBox(height: 15),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonTextWidget.PoppinsMedium(
                  text: "Net payable amount",
                  fontSize: 16,
                  color: black2D2,
                ),
                CommonTextWidget.PoppinsMedium(
                  text: "₹ 2944",
                  fontSize: 16,
                  color: black2D2,
                ),
              ],
            ),
            Spacer(),
            CommonButtonWidget.button(
              text: "Proceed to Pay",
              onTap: () {},
              buttonColor: yellowF9B,
            ),
            SizedBox(height: 50),
          ],
        ),
      ),
    );
  }
}
