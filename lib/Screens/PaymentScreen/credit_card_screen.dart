import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Utills/common_button_widget.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';


class PaymentScreen extends StatefulWidget {
  const PaymentScreen({Key? key}) : super(key: key);

  @override
  _PaymentScreenState createState() => _PaymentScreenState();
}

class _PaymentScreenState extends State<PaymentScreen> {
  final TextEditingController cardNumberController = TextEditingController();
  final TextEditingController expiryDateController = TextEditingController();
  final TextEditingController cvvController = TextEditingController();
  final TextEditingController cardHolderNameController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      appBar: AppBar(
        backgroundColor: white,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: black2D2),
          onPressed: () {
            Get.back();
          },
        ),
        title: CommonTextWidget.PoppinsMedium(
          text: "Ödeme Yap",
          fontSize: 20,
          color: black2D2,
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CommonTextWidget.PoppinsMedium(
              text: "Kart Bilgileri",
              fontSize: 18,
              color: black2D2,
            ),
            SizedBox(height: 20),
            TextField(
              controller: cardNumberController,
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                border: OutlineInputBorder(),
                labelText: "Kart Numarası",
                hintText: "1234 5678 9012 3456",
                prefixIcon: Icon(Icons.credit_card),
              ),
            ),
            SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: expiryDateController,
                    keyboardType: TextInputType.datetime,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(),
                      labelText: "Son Kullanma Tarihi",
                      hintText: "MM/YY",
                    ),
                  ),
                ),
                SizedBox(width: 10),
                Expanded(
                  child: TextField(
                    controller: cvvController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      border: OutlineInputBorder(),
                      labelText: "CVV",
                      hintText: "123",
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 20),
            TextField(
              controller: cardHolderNameController,
              decoration: InputDecoration(
                border: OutlineInputBorder(),
                labelText: "Kart Sahibi Adı",
              ),
            ),
            SizedBox(height: 30),
            Center(
              child: CommonButtonWidget.button(
                text: "Ödeme Yap",
                buttonColor: yellowF9B,
                onTap: () {
                //  Get.to(() => ReservationDone());

                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}