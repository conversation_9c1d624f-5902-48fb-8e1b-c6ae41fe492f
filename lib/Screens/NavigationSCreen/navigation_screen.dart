import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Controllers/navigation_controller.dart';
import 'package:fastyorderapp/Screens/CalendarScreens/calendar_screen.dart';
import 'package:fastyorderapp/Screens/HomeScreen/home_screen.dart';
import 'package:fastyorderapp/Screens/PrimeMemberShipScreen/prime_membership_screen.dart';
import 'package:fastyorderapp/Screens/ProfileScreens/profile_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class NavigationScreen extends StatelessWidget {
  final NavigationController navigationController =
      Get.put(NavigationController());

final pages = [
  HomeScreen(),
  CalendarScreen(),
  FavoriteFirmsWidget(userId: 'validUserId'), // Replace 'validUserId' with the actual user ID
  ProfileScreen(),
];

  buildMyNavBar(BuildContext context) {
    return Container(
      height: 70,
      decoration: BoxDecoration(
        color: white,
        boxShadow: [
          BoxShadow(
            color: black.withOpacity(0.08),
            blurRadius: 16,
            spreadRadius: 0,
            offset: Offset(0, -4),
          ),
        ],
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            buildNavItem(context, 0, Images.homeIcon),
            buildNavItem(context, 1, Images.calenderIcon),
            SizedBox(width: 40), // Space for the floating action button
            buildNavItem(context, 2, Images.starIcon),
            buildNavItem(context, 3, Images.userIcon),
          ],
        ),
      ),
    );
  }

Widget buildNavItem(BuildContext context, int index, String icon) {
  bool isFavoriteFirmsWidget = index ==5; // Assuming index 2 is FavoriteFirmsWidget

  return Obx(
    () => InkWell(
      enableFeedback: false,
      onTap: isFavoriteFirmsWidget
          ? null
          : () {
              navigationController.pageIndex.value = index;
            },
      child: AnimatedContainer(
        duration: Duration(milliseconds: 300),
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
        decoration: BoxDecoration(
          color: navigationController.pageIndex.value == index
              ? yellowF9B.withOpacity(0.2)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(30),
        ),
        child: SvgPicture.asset(
          icon,
          color: navigationController.pageIndex.value == index
              ? black2D2
              : greyB0B,
        ),
      ),
    ),
  );
}
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: black,
      body: Obx(
        () => Stack(
          alignment: Alignment.bottomCenter,
          children: [
            pages[navigationController.pageIndex.value],
            buildMyNavBar(context),
            Positioned(
              bottom: 35,
              child: FloatingActionButton(
                onPressed: () {
                  // Action for the floating button
                },
                backgroundColor: yellowF9B,
                child: Icon(Icons.shopping_cart, color: white),
              ),
            ),
          ],
        ),
      ),
    );
  }
}