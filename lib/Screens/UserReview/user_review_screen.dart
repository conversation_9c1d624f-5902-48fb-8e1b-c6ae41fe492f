import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Screens/PageViewScreens/page_view_screen.dart';
import 'package:fastyorderapp/Utills/common_button_widget.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:get/get.dart';

class UserReviewScreen extends StatelessWidget {
  UserReviewScreen({Key? key}) : super(key: key);

  final List<Map> reviewList = [
    {
      "image": Images.reviewImage,
      "text1": "Ahmet Yılmaz",
      "text2": "3 yorum, 3 ziyaret",
      "text3": "1 ay önce",
      "text4": "4.0",
      "grid": [
        "Yemek",
        "Hijyen",
        "Hizmet",
      ],
      "text5": "Baklava, Dondurma",
    },
    {
      "image": Images.reviewImage,
      "text1": "<PERSON><PERSON>",
      "text2": "4 yorum, 4 ziyaret",
      "text3": "1 ay önce",
      "text4": "4.0",
      "grid": [
        "Ambiyans",
        "Hizmet",
        "Yemek",
      ],
      "text5": "İskender, Lahmacun",
    },
    {
      "image": Images.reviewImage,
      "text1": "Mehmet Demir",
      "text2": "2 yorum, 2 ziyaret",
      "text3": "1 ay önce",
      "text4": "5.0",
      "grid": [
        "Ambiyans",
        "Hizmet",
        "Yemek",
      ],
      "text5": "Kebap, Pide",
    },
    {
      "image": Images.reviewImage,
      "text1": "Ayşe Çelik",
      "text2": "2 yorum, 2 ziyaret",
      "text3": "1 ay önce",
      "text4": "5.0",
      "grid": [
        "Yemek",
      ],
      "text5": "Künefe",
    },
    {
      "image": Images.reviewImage,
      "text1": "Fatma Şahin",
      "text2": "1 yorum, 1 ziyaret",
      "text3": "2 ay önce",
      "text4": "4.0",
      "grid": [
        "Ambiyans",
        "Hizmet",
        "Müzik",
        "Yemek",
        "Fiyat",
        "Hijyen",
      ],
      "text5": "Mercimek Çorbası",
    },
    {
      "image": Images.reviewImage,
      "text1": "Ali Vural",
      "text2": "16 yorum, 7 ziyaret",
      "text3": "2 ay önce",
      "text4": "5.0",
      "grid": [
        "Hizmet",
        "Ambiyans",
        "Yemek",
      ],
      "text5": "Adana Kebap, Lahmacun",
    },
    {
      "image": Images.reviewImage,
      "text1": "Murat",
      "text2": "8 yorum",
      "text3": "1 ay önce",
      "text4": "5.0",
      "grid": [
        "Hizmet",
      ],
      "text5": "Harika menü",
    },
    {
      "image": Images.reviewImage,
      "text1": "Zeynep",
      "text2": "5 yorum, 5 ziyaret",
      "text3": "1 ay önce",
      "text4": "5.0",
      "grid": [
        "Ambiyans",
        "Hizmet",
        "Yemek",
        "Hijyen",
      ],
      "text5": "Mantar Çorbası",
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      appBar: AppBar(
        backgroundColor: white,
        automaticallyImplyLeading: false,
        centerTitle: true,
        elevation: 1,
        leading: Padding(
          padding: EdgeInsets.only(left: 20),
          child: InkWell(
            onTap: () {
              Get.back();
            },
            child: Icon(
              Icons.arrow_back,
              color: black2D2,
              size: 24,
            ),
          ),
        ),
        title: CommonTextWidget.PoppinsMedium(
          text: "Kullanıcı Yorumları",
          fontSize: 18,
          color: black2D2,
        ),
      ),
      body: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          ScrollConfiguration(
            behavior: MyBehavior(),
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 12.5),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(top: 20, left: 11.5),
                      child: CommonTextWidget.PoppinsMedium(
                        text: "Tüm Kullanıcı Yorumları",
                        fontSize: 18,
                        color: black2D2,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 10, left: 11.5),
                      child: Row(
                        children: [
                          CommonTextWidget.PoppinsMedium(
                            text: "4.5",
                            fontSize: 36,
                            color: black2D2,
                          ),
                          SizedBox(
                            width: 8,
                          ),
                          CommonTextWidget.PoppinsRegular(
                            text: "(579 yorum)",
                            fontSize: 12,
                            color: black2D2,
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 11.5),
                      child: RatingBar.builder(
                        initialRating: 5,
                        minRating: 5,
                        direction: Axis.horizontal,
                        allowHalfRating: true,
                        itemCount: 5,
                        ignoreGestures: true,
                        itemSize: 21,
                        itemPadding: EdgeInsets.symmetric(horizontal: 4.0),
                        itemBuilder: (context, _) => Icon(
                          Icons.star,
                          color: Colors.amber,
                        ),
                        onRatingUpdate: (rating) {
                          print(rating);
                        },
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 20, left: 11.5, bottom: 12),
                      child: CommonTextWidget.PoppinsRegular(
                        text: "İstanbul'daki restoranlar arasında 4. sırada",
                        fontSize: 12,
                        color: black2D2,
                      ),
                    ),
                    ListView.builder(
                      shrinkWrap: true,
                      itemCount: reviewList.length,
                      physics: NeverScrollableScrollPhysics(),
                      padding: EdgeInsets.zero,
                      itemBuilder: (context, index) {
                        return Column(
                          children: [
                            Divider(
                              height: 1,
                              thickness: 1,
                              color: blackD5D,
                            ),
                            Container(
                              margin: EdgeInsets.symmetric(
                                  horizontal: 11.5, vertical: 12),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Colors.green,
                                          image: DecorationImage(
                                            image: AssetImage(
                                              reviewList[index]["image"],
                                            ),
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                        height: 35,
                                        width: 35,
                                      ),
                                      SizedBox(
                                        width: 10,
                                      ),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            CommonTextWidget.PoppinsMedium(
                                              text: reviewList[index]["text1"],
                                              fontSize: 12,
                                              color: black2D2,
                                            ),
                                            SizedBox(
                                              height: 7,
                                            ),
                                            CommonTextWidget.PoppinsRegular(
                                              text: reviewList[index]["text2"],
                                              fontSize: 10,
                                              color: black616,
                                            ),
                                          ],
                                        ),
                                      ),
                                      CommonTextWidget.PoppinsRegular(
                                        text: reviewList[index]["text3"],
                                        fontSize: 10,
                                        color: black616,
                                      ),
                                    ],
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        top: 14, bottom: 20),
                                    child: Row(
                                      children: [
                                        CommonTextWidget.PoppinsMedium(
                                          text: reviewList[index]["text4"],
                                          fontSize: 16,
                                          color: black2D2,
                                        ),
                                        SizedBox(
                                          width: 10,
                                        ),
                                        RatingBar.builder(
                                          initialRating: 5,
                                          minRating: 5,
                                          direction: Axis.horizontal,
                                          allowHalfRating: true,
                                          itemCount: 5,
                                          ignoreGestures: true,
                                          itemSize: 21,
                                          itemPadding: EdgeInsets.symmetric(
                                              horizontal: 4.0),
                                          itemBuilder: (context, _) => Icon(
                                            Icons.star,
                                            color: Colors.amber,
                                          ),
                                          onRatingUpdate: (rating) {
                                            print(rating);
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                                  LayoutBuilder(
                                    builder: (context, constraints) =>
                                        GridView.count(
                                      padding: EdgeInsets.zero,
                                      childAspectRatio: Get.width > 360
                                          ? MediaQuery.of(context)
                                                  .size
                                                  .aspectRatio *
                                              3 /
                                              0.5
                                          : MediaQuery.of(context)
                                                  .size
                                                  .aspectRatio *
                                              3 /
                                              0.7,
                                      shrinkWrap: true,
                                      crossAxisCount: 3,
                                      physics: NeverScrollableScrollPhysics(),
                                      crossAxisSpacing: 15,
                                      mainAxisSpacing: 15,
                                      children: List.generate(
                                        reviewList[index]["grid"].length,
                                        (index2) => Container(
                                          height: 10,
                                          width: 20,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            border: Border.all(
                                                color: grey939, width: 1),
                                          ),
                                          child: Center(
                                            child:
                                                CommonTextWidget.PoppinsMedium(
                                              text: reviewList[index]["grid"][index2],
                                              fontSize: 12,
                                              color: grey939,
                                            ),
                                          ),
                                        ),
                                      ).toList(),
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsets.only(top: 20),
                                    child: CommonTextWidget.PoppinsMedium(
                                      text: "Önerilen yemekler",
                                      fontSize: 12,
                                      color: black2D2,
                                    ),
                                  ),
                                  Padding(
                                    padding: EdgeInsets.only(top: 2),
                                    child: CommonTextWidget.PoppinsMedium(
                                      text: reviewList[index]["text5"],
                                      fontSize: 12,
                                      color: black616,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                    SizedBox(height: 100),
                  ],
                ),
              ),
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: white,
              boxShadow: [
                BoxShadow(
                  color: black.withOpacity(0.08),
                  spreadRadius: 0,
                  offset: Offset(0, -4),
                  blurRadius: 16,
                ),
              ],
            ),
            child: Padding(
              padding:
                  EdgeInsets.only(top: 15, bottom: 30, left: 25, right: 20),
              child: Row(
                children: [
                  Expanded(
                    child: CommonButtonWidget.button(
                      text: "Yorum Yaz",
                      buttonColor: yellowF9B,
                      onTap: () {
                        Get.to(() => PageViewScreen());
                      },
                    ),
                  ),
                  SizedBox(width: 15),
                  Expanded(
                    child: CommonButtonWidget.button(
                      text: "Rezervasyon Yap",
                      buttonColor: yellowF9B,
                      onTap: () {
                        Get.back();
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}