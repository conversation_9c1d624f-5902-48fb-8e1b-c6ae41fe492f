import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Controllers/page_view_controller.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class PageViewScreen1 extends StatelessWidget {
  PageViewScreen1({Key? key}) : super(key: key);

  final PageViewController pageViewController = Get.put(PageViewController());
  final PageViewController1 pageViewController1 =
      Get.put(PageViewController1());

  @override
  Widget build(BuildContext context) {
    return ScrollConfiguration(
      behavior: My<PERSON><PERSON>avi<PERSON>(),
      child: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: 50),
            SvgPicture.asset(Images.hotPotImage),
            SizedBox(height: 80),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 25),
              child: CommonTextWidget.PoppinsMedium(
                text: "Sümerli Hub genel deneyiminiz nasıldı?",
                color: black2D2,
                fontSize: 18,
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 35),
            RatingBar(
              itemSize: 30,
              maxRating: 5,
              initialRating: 0,
              glow: false,
              itemPadding: EdgeInsets.only(right: 10),
              itemCount: 5,
              direction: Axis.horizontal,
              ratingWidget: RatingWidget(
                full: Icon(
                  Icons.star,
                  color: yellowF9B,
                ),
                empty: Icon(
                  Icons.star_border,
                  color: grey7B7,
                ),
                half: Icon(Icons.star, color: grey7B7),
              ),
              onRatingUpdate: (rating) {},
            ),
            SizedBox(height: 30),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 40),
              child: CommonTextWidget.PoppinsMedium(
                text: "Harika! En çok neyi beğendiniz?",
                color: black2D2,
                fontSize: 18,
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 35),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Obx(
                    () => InkWell(
                      onTap: () {
                        if (pageViewController.one.isTrue ||
                            pageViewController.two.isTrue ||
                            pageViewController.three.isTrue) {
                          pageViewController.one(false);
                          pageViewController.two(false);
                          pageViewController.three(false);
                        }
                        pageViewController.one(true);
                      },
                      child: Container(
                        height: 38,
                        width: 85,
                        decoration: BoxDecoration(
                          border: Border.all(
                              color: pageViewController.one.isTrue
                                  ? yellowF9B
                                  : black616,
                              width: 1),
                          borderRadius: BorderRadius.circular(6),
                          color:
                              pageViewController.one.isTrue ? yellowF9B : white,
                        ),
                        child: Center(
                          child: CommonTextWidget.PoppinsMedium(
                            color: pageViewController.one.isTrue
                                ? white
                                : black616,
                            fontSize: 12,
                            text: "Ambiyans",
                          ),
                        ),
                      ),
                    ),
                  ),
                  Obx(
                    () => InkWell(
                      onTap: () {
                        if (pageViewController.one.isTrue ||
                            pageViewController.two.isTrue ||
                            pageViewController.three.isTrue) {
                          pageViewController.one(false);
                          pageViewController.two(false);
                          pageViewController.three(false);
                        }
                        pageViewController.two(true);
                      },
                      child: Container(
                        height: 38,
                        width: 85,
                        decoration: BoxDecoration(
                          border: Border.all(
                              color: pageViewController.two.isTrue
                                  ? yellowF9B
                                  : black616,
                              width: 1),
                          borderRadius: BorderRadius.circular(6),
                          color:
                              pageViewController.two.isTrue ? yellowF9B : white,
                        ),
                        child: Center(
                          child: CommonTextWidget.PoppinsMedium(
                            color: pageViewController.two.isTrue
                                ? white
                                : black616,
                            fontSize: 12,
                            text: "Servis",
                          ),
                        ),
                      ),
                    ),
                  ),
                  Obx(
                    () => InkWell(
                      onTap: () {
                        if (pageViewController.one.isTrue ||
                            pageViewController.two.isTrue ||
                            pageViewController.three.isTrue) {
                          pageViewController.one(false);
                          pageViewController.two(false);
                          pageViewController.three(false);
                        }
                        pageViewController.three(true);
                      },
                      child: Container(
                        height: 38,
                        width: 85,
                        decoration: BoxDecoration(
                          border: Border.all(
                              color: pageViewController.three.isTrue
                                  ? yellowF9B
                                  : black616,
                              width: 1),
                          borderRadius: BorderRadius.circular(6),
                          color: pageViewController.three.isTrue
                              ? yellowF9B
                              : white,
                        ),
                        child: Center(
                          child: CommonTextWidget.PoppinsMedium(
                            color: pageViewController.three.isTrue
                                ? white
                                : black616,
                            fontSize: 12,
                            text: "Müzik",
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 10),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 45),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Obx(
                    () => InkWell(
                      onTap: () {
                        if (pageViewController1.one.isTrue ||
                            pageViewController1.two.isTrue ||
                            pageViewController1.three.isTrue) {
                          pageViewController1.one(false);
                          pageViewController1.two(false);
                          pageViewController1.three(false);
                        }
                        pageViewController1.one(true);
                      },
                      child: Container(
                        height: 38,
                        width: 80,
                        decoration: BoxDecoration(
                          border: Border.all(
                              color: pageViewController1.one.isTrue
                                  ? yellowF9B
                                  : black616,
                              width: 1),
                          borderRadius: BorderRadius.circular(6),
                          color: pageViewController1.one.isTrue
                              ? yellowF9B
                              : white,
                        ),
                        child: Center(
                          child: CommonTextWidget.PoppinsMedium(
                            color: pageViewController1.one.isTrue
                                ? white
                                : black616,
                            fontSize: 12,
                            text: "Yemek",
                          ),
                        ),
                      ),
                    ),
                  ),
                  Obx(
                    () => InkWell(
                      onTap: () {
                        if (pageViewController1.one.isTrue ||
                            pageViewController1.two.isTrue ||
                            pageViewController1.three.isTrue) {
                          pageViewController1.one(false);
                          pageViewController1.two(false);
                          pageViewController1.three(false);
                        }
                        pageViewController1.two(true);
                      },
                      child: Container(
                        height: 38,
                        width: 80,
                        decoration: BoxDecoration(
                          border: Border.all(
                              color: pageViewController1.two.isTrue
                                  ? yellowF9B
                                  : black616,
                              width: 1),
                          borderRadius: BorderRadius.circular(6),
                          color: pageViewController1.two.isTrue
                              ? yellowF9B
                              : white,
                        ),
                        child: Center(
                          child: CommonTextWidget.PoppinsMedium(
                            color: pageViewController1.two.isTrue
                                ? white
                                : black616,
                            fontSize: 12,
                            text: "Fiyat",
                          ),
                        ),
                      ),
                    ),
                  ),
                  Obx(
                    () => InkWell(
                      onTap: () {
                        if (pageViewController1.one.isTrue ||
                            pageViewController1.two.isTrue ||
                            pageViewController1.three.isTrue) {
                          pageViewController1.one(false);
                          pageViewController1.two(false);
                          pageViewController1.three(false);
                        }
                        pageViewController1.three(true);
                      },
                      child: Container(
                        height: 38,
                        width: 80,
                        decoration: BoxDecoration(
                          border: Border.all(
                              color: pageViewController1.three.isTrue
                                  ? yellowF9B
                                  : black616,
                              width: 1),
                          borderRadius: BorderRadius.circular(6),
                          color: pageViewController1.three.isTrue
                              ? yellowF9B
                              : white,
                        ),
                        child: Center(
                          child: CommonTextWidget.PoppinsMedium(
                            color: pageViewController1.three.isTrue
                                ? white
                                : black616,
                            fontSize: 12,
                            text: "Hijyen",
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
