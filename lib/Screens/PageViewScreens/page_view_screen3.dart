import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/font_family.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class PageViewScreen3 extends StatelessWidget {
  PageViewScreen3({Key? key}) : super(key: key);

  final TextEditingController pageView3Controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return ScrollConfiguration(
      behavior: MyBehavior(),
      child: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: 50),
            SvgPicture.asset(Images.hotPotImage),
            SizedBox(height: 80),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 25),
              child: CommonTextWidget.PoppinsMedium(
                text: "Menüdeki favorileriniz neler?",
                color: black2D2,
                fontSize: 18,
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 40),
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 25),
                child: CommonTextWidget.PoppinsLight(
                  text: "Beğendiğiniz yemek/içeceği yazın",
                  color: black2D2,
                  fontSize: 14,
                ),
              ),
            ),
            SizedBox(height: 25),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 25),
              child: Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      keyboardType: TextInputType.text,
                      cursorColor: black2D2,
                      controller: pageView3Controller,
                      style: TextStyle(
                        color: black2D2,
                        fontSize: 14,
                        fontFamily: FontFamily.PoppinsRegular,
                      ),
                      decoration: InputDecoration(
                        hintText: "Örneğin: Tavuk şiş",
                        hintStyle: TextStyle(
                          color: grey949,
                          fontSize: 12,
                          fontFamily: FontFamily.PoppinsRegular,
                        ),
                        filled: true,
                        fillColor: white,
                        contentPadding: EdgeInsets.zero,
                        disabledBorder: UnderlineInputBorder(
                            borderSide: BorderSide(color: greyE2E, width: 1)),
                        border: UnderlineInputBorder(
                            borderSide: BorderSide(color: greyE2E, width: 1)),
                        focusedBorder: UnderlineInputBorder(
                            borderSide: BorderSide(color: greyE2E, width: 1)),
                        enabledBorder: UnderlineInputBorder(
                            borderSide: BorderSide(color: greyE2E, width: 1)),
                        errorBorder: UnderlineInputBorder(
                            borderSide: BorderSide(color: greyE2E, width: 1)),
                      ),
                    ),
                  ),
                  SizedBox(width: 30),
                  Image.asset(Images.addImage, height: 45, width: 45),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
