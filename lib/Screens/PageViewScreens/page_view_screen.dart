import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Controllers/navigation_controller.dart';
import 'package:fastyorderapp/Screens/NavigationSCreen/navigation_screen.dart';
import 'package:fastyorderapp/Screens/PageViewScreens/page_view_screen1.dart';
import 'package:fastyorderapp/Screens/PageViewScreens/page_view_screen3.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class PageViewScreen extends StatefulWidget {
  const PageViewScreen({Key? key}) : super(key: key);

  @override
  State<PageViewScreen> createState() => _PageViewScreenState();
}

class _PageViewScreenState extends State<PageViewScreen> {
  final NavigationController navigationController =
  Get.put(NavigationController());
  PageController pageController = PageController(initialPage: 0);
  int index = 0;
  final List screenList = [
    PageViewScreen1(),
    PageViewScreen3(),
  ];

  @override
  Widget build(BuildContext context) {
    dialogue() {
      showDialog(
        context: context,
        builder: (_) => AlertDialog(
          backgroundColor: white,
          contentPadding: EdgeInsets.zero,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                height: 160,
                width: Get.width,
                decoration: BoxDecoration(
                  color: yellowFFF1,
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(8),
                    topLeft: Radius.circular(8),
                  ),
                ),
                child: Center(
                  child: SvgPicture.asset(Images.hotPotImage),
                ),
              ),
              SizedBox(height: 35),
              CommonTextWidget.PoppinsMedium(
                text: "Neredeyse bitirdiniz!",
                color: yellowF9B,
                fontSize: 18,
              ),
              SizedBox(height: 45),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  InkWell(
                    onTap: (){
                      navigationController.pageIndex.value = 0;
                      Get.off(() => NavigationScreen());
                    },
                    child:  CommonTextWidget.PoppinsRegular(
                      text: "Evet, Çık",
                      color: grey9E9,
                      fontSize: 14,
                    ),
                  ),
                  SizedBox(width: 60),
                  MaterialButton(
                    onPressed: () {
                      navigationController.pageIndex.value = 0;
                      Get.off(() => NavigationScreen());
                    },
                    height: 38,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    color: yellowF9B,
                    child: CommonTextWidget.PoppinsMedium(
                      fontSize: 15,
                      text: "Hayır, Devam Et",
                      color: white,
                    ),
                  )
                ],
              ),
              SizedBox(height: 35),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: white,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: Get.width,
            decoration: BoxDecoration(
              color: white,
              boxShadow: [
                BoxShadow(
                  color: black.withOpacity(0.08),
                  blurRadius: 16,
                  offset: Offset(0, 0),
                  spreadRadius: 0,
                ),
              ],
            ),
            child: Padding(
              padding:
              EdgeInsets.only(left: 25, right: 25, top: 50, bottom: 20),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: Icon(
                      Icons.arrow_back,
                      color: black2D2,
                      size: 24,
                    ),
                  ),
                  SizedBox(width: 25),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CommonTextWidget.PoppinsMedium(
                        text: "",
                        color: black2D2,
                        fontSize: 14,
                      ),
                      CommonTextWidget.PoppinsRegular(
                        text: "",
                        color: grey8D8,
                        fontSize: 12,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: ScrollConfiguration(
              behavior: MyBehavior(),
              child: PageView.builder(
                itemCount: screenList.length,
                controller: pageController,
                scrollDirection: Axis.horizontal,
                onPageChanged: (i) {
                  setState(
                        () {
                      index = i;
                    },
                  );
                },
                itemBuilder: (context, index) => screenList[index],
              ),
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 25, right: 25, bottom: 50),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonTextWidget.PoppinsMedium(
                  text: "Geç",
                  color: white,
                  fontSize: 18,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    screenList.length,
                        (position) => Padding(
                      padding: EdgeInsets.only(right: 8),
                      child: Container(
                        width: 10,
                        height: 10,
                        decoration: BoxDecoration(
                          color: index == position ? yellowF9B : greyD8D,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ),
                ),
                index == screenList.length - 1
                    ? MaterialButton(
                  onPressed: () {
                    dialogue();
                  },
                  height: 38,
                  minWidth: 87,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  color: yellowF9B,
                  child: CommonTextWidget.PoppinsMedium(
                    fontSize: 15,
                    text: "Gönder",
                    color: white,
                  ),
                )
                    : InkWell(
                  onTap: () {
                    pageController.nextPage(
                        duration: 300.milliseconds, curve: Curves.ease);
                  },
                  child: CommonTextWidget.PoppinsMedium(
                    text: "İleri",
                    color: yellowF9B,
                    fontSize: 18,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
