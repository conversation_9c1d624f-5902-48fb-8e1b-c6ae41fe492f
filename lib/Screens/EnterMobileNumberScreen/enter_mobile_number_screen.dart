import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Screens/NavigationSCreen/navigation_screen.dart';
import 'package:fastyorderapp/Screens/OtpScreen/otp_screen.dart';
import 'package:fastyorderapp/Utills/common_button_widget.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/Utills/common_textfield_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:firebase_auth/firebase_auth.dart';

class EnterMobileNumberScreen extends StatelessWidget {
  EnterMobileNumberScreen({Key? key}) : super(key: key);

  final TextEditingController numberController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      resizeToAvoidBottomInset: false,
      body: SingleChildScrollView(
        child: <PERSON>umn(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 60),

            /// Top Widget View
            TopWidgetView(),

            /// Body Widget View
            BodyWidgetView(),

            SizedBox(height: 60),
            Padding(
              padding: EdgeInsets.only(
                left: 25,
                right: 25,
              ),
              child: CommonButtonWidget.button(
                text: "SMS Gönder",
                buttonColor: yellowF9B,
                onTap: () async {
                  await _sendOtp(context);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _sendOtp(BuildContext context) async {
    if (numberController.text.isEmpty || numberController.text.length != 10) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text("Uyarı"),
            content: Text("Lütfen geçerli bir 10 haneli telefon numarası girin."),
            actions: [
              TextButton(
                child: Text("Tamam"),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ],
          );
        },
      );
      return;
    }

    try {
      final phoneNumber = '+90${numberController.text}';
      final auth = FirebaseAuth.instance;

      await auth.verifyPhoneNumber(
        phoneNumber: phoneNumber,
        timeout: const Duration(seconds: 60),
        verificationCompleted: (PhoneAuthCredential credential) async {
          try {
            await auth.signInWithCredential(credential);
            Get.to(() => NavigationScreen());
          } catch (e) {
            print("Auto verification error: $e");
          }
        },
        verificationFailed: (FirebaseAuthException e) {
          String message = "Doğrulama başarısız oldu";
          if (e.code == 'invalid-phone-number') {
            message = "Geçersiz telefon numarası formatı";
          }

          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text("Hata"),
              content: Text(message),
              actions: [
                TextButton(
                  child: Text("Tamam"),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ],
            ),
          );
        },
        codeSent: (String verificationId, int? resendToken) {
          Get.to(() => OtpScreen(verificationId: verificationId));
        },
        codeAutoRetrievalTimeout: (String verificationId) {
          print("Auto retrieval timeout");
        },
      );
    } catch (e) {
      print("Error sending OTP: $e");
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text("Hata"),
          content: Text("SMS gönderilirken bir hata oluştu. Lütfen tekrar deneyin."),
          actions: [
            TextButton(
              child: Text("Tamam"),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ],
        ),
      );
    }
  }

  /// Top Widget View
  Widget TopWidgetView() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 25),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Icon(Icons.close, size: 20, color: black2D2),
          InkWell(
            onTap: () {
              Get.to(() => NavigationScreen());
            },
            child: CommonTextWidget.PoppinsMedium(
              text: "",
              color: black2D2,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// Body Widget View
  Widget BodyWidgetView() {
    return Center(
      child: Column(
        children: [
          SizedBox(height: 52),
          CommonTextWidget.PoppinsMedium(
            text: "Telefon Numaranız",
            color: black2D2,
            fontSize: 22,
          ),
          SizedBox(height: 10),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20),
            child: CommonTextWidget.PoppinsRegular(
              text:
              "Mobil numaranız, rezervasyonunuz, siparişleriniz ve harika teklifler için size ulaşmamıza yardımcı olur",
              color: grey8A8,
              fontSize: 12,
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: 35),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25),
            child: CommonTextFieldWidget.TextFormField1(
              hintText: "Telefon numaranız",
              keyboardType: TextInputType.text,
              controller: numberController,
              prefixIcon: Padding(
                padding: EdgeInsets.only(left: 15),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(width: 4),
                    CommonTextWidget.PoppinsRegular(
                      text: "+90",
                      color: black2D2,
                      fontSize: 17,
                    ),
                    SizedBox(width: 12),
                  ],
                ),
              ),
              enabled: true,
            ),
          ),
        ],
      ),
    );
  }
}