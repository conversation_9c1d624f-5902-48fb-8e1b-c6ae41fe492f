import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/font_family.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Screens/NavigationSCreen/navigation_screen.dart';
import 'package:fastyorderapp/Utills/common_button_widget.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/main.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class SelectLocationScreen extends StatelessWidget {
  SelectLocationScreen({Key? key}) : super(key: key);
  final TextEditingController searchController = TextEditingController();
  final List<Map> listViewList = [
    {
      "text1": "All of Surat",
      "text2": "Surat",
    },
    {
      "text1": "Piplod",
      "text2": "Surat, Surat",
    },
    {
      "text1": "VIP Plaza",
      "text2": "Vesu, Surat",
    },
    {
      "text1": "Park Inn by Redisson",
      "text2": "Surat",
    },
    {
      "text1": "Suraj Kund",
      "text2": "Faridabad",
    },
    {
      "text1": "Sarat bose road",
      "text2": "Kolkata",
    },
    {
      "text1": "Suraj Kund",
      "text2": "Sauth Delhi",
    },
    {
      "text1": "Vivanta by Taj",
      "text2": "Suraj Kund",
    },
    {
      "text1": "Chander Nagar",
      "text2": "East Delhi",
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        backgroundColor: white,
        automaticallyImplyLeading: false,
        centerTitle: true,
        elevation: 0,
        leading: Padding(
          padding: EdgeInsets.only(left: 20),
          child: InkWell(
            onTap: () {
              Get.back();
            },
            child: Icon(
              Icons.arrow_back,
              color: black2D2,
              size: 24,
            ),
          ),
        ),
        title: CommonTextWidget.PoppinsMedium(
          text: "Select Location",
          fontSize: 18,
          color: black2D2,
        ),
      ),
      body: ScrollConfiguration(
        behavior: MyBehavior(),
        child: SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(height: 50),

              /// Button Widget View
              ButtonWidgetView(),

              /// SearchFieldAndList Widget View
              SearchFieldAndListWidgetView(),
            ],
          ),
        ),
      ),
    );
  }

  /// Button Widget View
  Widget ButtonWidgetView() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 25),
      child: CommonButtonWidget.button(
        text: "Konumumu Kullan",
        buttonColor: yellowF9B,
        onTap: () {
          Get.to(() => NavigationScreen());
        },
      ),
    );
  }

  /// SearchFieldAndList Widget View
  Widget SearchFieldAndListWidgetView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 30),
        TextFormField(
          keyboardType: TextInputType.text,
          cursorColor: black2D2,
          controller: searchController,
          style: TextStyle(
            color: black2D2,
            fontSize: 14,
            fontFamily: FontFamily.PoppinsRegular,
          ),
          decoration: InputDecoration(
            hintText: "",
            hintStyle: TextStyle(
              color: grey949,
              fontSize: 14,
              fontFamily: FontFamily.PoppinsRegular,
            ),
            prefixIcon: Padding(
              padding: EdgeInsets.only(left: 15),
              child: Icon(CupertinoIcons.search, color: grey949, size: 18),
            ),
            suffixIcon: Padding(
              padding: EdgeInsets.only(right: 25, top: 15, bottom: 15),
              child: SvgPicture.asset(Images.closeIcon),
            ),
            filled: true,
            fillColor: white,
            contentPadding: EdgeInsets.zero,
            border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: greyE2E, width: 1)),
            focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: greyE2E, width: 1)),
            enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: greyE2E, width: 1)),
            errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: greyE2E, width: 1)),
          ),
        ),
        SizedBox(height: 30),

        SizedBox(height: 15),

      ],
    );
  }
}
