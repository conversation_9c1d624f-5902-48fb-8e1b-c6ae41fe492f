import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';

import '../../Constants/colors.dart';
import '../../Controllers/cafe_list_tab_controller.dart';
import 'firms_screen.dart';

class CafeListScreen extends StatelessWidget {
  CafeListScreen({Key? key}) : super(key: key);

  final CafeListTabController cafeListTabController =
  Get.put(CafeListTabController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// Top Container Widget View
          TopContainerWidgetView(),

          /// Tab Bar View ile PopularRestaurantsWidget
          Expanded(
            child: TabBarView(
              controller: cafeListTabController.controller,
              children: [
                // Her tab için PopularRestaurantsWidget
                PopularRestaurantsWidget(),
                PopularRestaurantsWidget(),
                PopularRestaurantsWidget(),
                PopularRestaurantsWidget(),
                PopularRestaurantsWidget(),
                PopularRestaurantsWidget(),
                PopularRestaurantsWidget(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Top Container Widget View
  Widget TopContainerWidgetView() {
    return Container(
      width: Get.width,
      decoration: BoxDecoration(
        color: white,
        boxShadow: [
          BoxShadow(
            color: black.withOpacity(0.08),
            blurRadius: 16,
            spreadRadius: 0,
            offset: Offset(0, 0),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.only(top: 55, left: 25, right: 25, bottom: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    InkWell(
                      onTap: () {
                        Get.back();
                      },
                      child: Icon(
                        Icons.arrow_back,
                        color: black2D2,
                        size: 24,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 22),
            Container(
              height: 40,
              width: Get.width,
              color: white,
              child: TabBar(
                isScrollable: true,
                tabs: cafeListTabController.myTabs,
                unselectedLabelColor: grey969,
                labelStyle: TextStyle(fontFamily: "PoppinsMedium", fontSize: 18),
                unselectedLabelStyle: TextStyle(fontFamily: "PoppinsMedium", fontSize: 18),
                labelColor: yellowF9B,
                controller: cafeListTabController.controller,
                indicatorColor: yellowF9B,
                indicatorWeight: 1.25,
              ),
            ),
          ],
        ),
      ),
    );
  }
}