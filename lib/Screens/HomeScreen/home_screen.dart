import 'package:carousel_slider/carousel_slider.dart';
import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Screens/HomeScreen/new_firms_screen.dart';
import 'package:fastyorderapp/Screens/HomeScreen/search_screen.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/Utills/common_textfield_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:fastyorderapp/providers/user_provider.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';

import '../../main.dart';
import 'cafe_list_screen.dart';
import 'firms_screen.dart';

class HomeScreen extends StatefulWidget {
  HomeScreen({Key? key}) : super(key: key);

  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final TextEditingController searchController = TextEditingController();
  String? _currentCity;

  final List<Map> whatsYouPickList = [
    {"image": Images.whatsYourPickImage1, "text": "Kahve"},
    {"image": Images.whatsYourPickImage2, "text": "Kahvaltı"},
    {"image": Images.whatsYourPickImage3, "text": "Akşam Yemeği"},
    {"image": Images.whatsYourPickImage4, "text": "Fast Food"},
  ];

  final List<Map> bannerViewList = [
    {"image": Images.homeBanner1},
    {"image": Images.homeBanner2},
    {"image": Images.homeBanner3},
  ];

  final List<Map> curatedCollectionsList = [
    {"image": Images.freeDrink, "text": "En İyi\nİçecekler"},
    {"image": Images.bestOfPrime, "text": "En\nSevilenler"},
    {"image": Images.upto20OffDeals, "text": "%20 \nİndirimliler"},
    {"image": Images.buffet, "text": "En İyi\n Dönerler"},
    {"image": Images.dessert, "text": " En İyi\nTatlılar"},
    {"image": Images.newlyOpened, "text": "Yeni \nAçılanlar"},
    {"image": Images.luxuryDining, "text": "Lüks Akşam \n Yemekleri"},
    {"image": Images.veggieFriendly, "text": "Vegan \nDostu"},
  ];

  @override
  void initState() {
    super.initState();
    _checkLocationPermissionAndFetch();
  }

  Future<void> _checkLocationPermissionAndFetch() async {
    bool serviceEnabled;
    LocationPermission permission;

    serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      _showLocationError("Konum servisini açmanız gerekiyor.");
      return;
    }

    permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        _showLocationError("Konum izni verilmedi. Bu özellik için konum izni zorunludur.");
        return;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      _showLocationError("Konum izni kalıcı olarak reddedildi. Lütfen ayarlardan izin verin.");
      return;
    }

    Position position = await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high);
    _getCityFromPosition(position);
  }

  Future<void> _getCityFromPosition(Position position) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(position.latitude, position.longitude);
      Placemark place = placemarks[0];
      setState(() {
        _currentCity = place.locality;
      });
    } catch (e) {
      _showLocationError("Şehir bilgisi alınamadı.");
    }
  }

  void _showLocationError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  Widget build(BuildContext context) {
    final userProvider = Provider.of<UserProvider>(context);
    final userName = userProvider.name;

    return Scaffold(
      backgroundColor: white,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TopContainerWidgetView(context, userName),
          WhatsYourPickWidgetView(),
          SizedBox(height: 100),
        ],
      ),
    );
  }

  Widget TopContainerWidgetView(BuildContext context, String? userName) {
    return Container(
      width: Get.width,
      decoration: BoxDecoration(
        color: white,
        boxShadow: [
          BoxShadow(
            color: black.withOpacity(0.08),
            blurRadius: 16,
            spreadRadius: 0,
            offset: Offset(0, 0),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.only(top: 55, left: 25, right: 25, bottom: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InkWell(
                  onTap: () {
                    _checkLocationPermissionAndFetch();
                  },
                  child: Row(
                    children: [
                      Icon(
                        CupertinoIcons.location_solid,
                        color: black2D2,
                        size: 20,
                      ),
                      SizedBox(width: 5),
                      Text(
                        _currentCity != null ? _currentCity! : 'Konumumu Göster',
                        style: TextStyle(
                          color: black2D2,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                if (userName != null)
                  Text(
                    'Hoşgeldiniz, $userName',
                    style: TextStyle(
                      color: black2D2,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
              ],
            ),
            SizedBox(height: 15),
            InkWell(
              onTap: () {
                Get.to(() => SearchScreen());
              },
              child: CommonTextFieldWidget.TextFormField1(
                prefixIcon: Icon(
                  CupertinoIcons.search,
                  color: black2D2,
                  size: 20,
                ),
                controller: searchController,
                keyboardType: TextInputType.text,
                hintText: "Konum, Mekan ara..",
                enabled: false,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget WhatsYourPickWidgetView() {
    return Expanded(
      child: ScrollConfiguration(
        behavior: MyBehavior(),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 25, vertical: 15),
                child: CommonTextWidget.PoppinsMedium(
                  text: "Merhaba, Ne İstersin?",
                  fontSize: 16,
                  color: black2D2,
                ),
              ),
              SizedBox(
                height: 95,
                child: ListView.builder(
                  shrinkWrap: true,
                  padding: EdgeInsets.only(left: 25, right: 2),
                  itemCount: whatsYouPickList.length,
                  scrollDirection: Axis.horizontal,
                  itemBuilder: (context, index) => Padding(
                    padding: EdgeInsets.only(right: 23),
                    child: InkWell(
                      onTap: () {
                        Get.to(() => CafeListScreen());
                      },
                      child: Column(
                        children: [
                          Image.asset(whatsYouPickList[index]["image"],
                              height: 70, width: 70),
                          SizedBox(height: 8),
                          CommonTextWidget.PoppinsMedium(
                            text: whatsYouPickList[index]["text"],
                            fontSize: 12,
                            color: black2D2,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(height: 20),
              BannerWidgetView(),
              PopularRestaurantsWidget(), // Using the new widget here
              CuratedCollectionsWidgetView(),
              NewRestaurantsWidget(), // Using the new widget here

              FeaturedRestaurantsBannerWidgetView(),
            ],
          ),
        ),
      ),
    );
  }

  Widget BannerWidgetView() {
    return CarouselSlider.builder(
      itemCount: bannerViewList.length,
      itemBuilder: (BuildContext context, index, int pageViewIndex) => Padding(
        padding: EdgeInsets.symmetric(horizontal: 25),
        child: InkWell(
          onTap: () {
            Get.to(() => CafeListScreen());
          },
          child: Container(
            height: 135,
            width: Get.width,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              image: DecorationImage(
                image: AssetImage(bannerViewList[index]["image"]),
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
      ),
      options: CarouselOptions(
          height: 180,
          autoPlay: true,
          autoPlayAnimationDuration: Duration(milliseconds: 800),
          enlargeCenterPage: true,
          scrollDirection: Axis.horizontal,
          initialPage: 3,
          viewportFraction: 0.99,
          onPageChanged: (index, reason) {}),
    );
  }

  Widget CuratedCollectionsWidgetView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 25, vertical: 20),
          child: CommonTextWidget.PoppinsMedium(
            text: "Özel Koleksiyonlar",
            fontSize: 16,
            color: black2D2,
          ),
        ),
        Container(
          height: 150,
          width: Get.width,
          child: ListView.builder(
            shrinkWrap: true,
            scrollDirection: Axis.horizontal,
            padding: EdgeInsets.only(left: 25, right: 5),
            itemCount: curatedCollectionsList.length,
            itemBuilder: (context, index) => Padding(
              padding: EdgeInsets.only(right: 20),
              child: InkWell(
                onTap: () {
                  Get.to(CafeListScreen());
                },
                child: Container(
                  height: 150,
                  width: 140,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                    image: DecorationImage(
                      image: AssetImage(curatedCollectionsList[index]["image"]),
                      fit: BoxFit.fill,
                    ),
                  ),
                  child: Stack(
                    alignment: Alignment.bottomLeft,
                    children: [
                      Image.asset(Images.homecanvasImage,
                          fit: BoxFit.fill, height: 150, width: 260),
                      Positioned(
                        left: 15,
                        bottom: 12,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CommonTextWidget.PoppinsBold(
                              text: curatedCollectionsList[index]["text"],
                              color: white,
                              fontSize: 18,
                            ),
                            SizedBox(height: 5),
                            CommonTextWidget.PoppinsRegular(
                              text: "Tümünü Gör >",
                              color: white,
                              fontSize: 12,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget FeaturedRestaurantsBannerWidgetView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 25, bottom: 20, top: 15, right: 25),
          child: CommonTextWidget.PoppinsMedium(
            text: "Kampanyalar",
            fontSize: 16,
            color: black2D2,
          ),
        ),
        CarouselSlider.builder(
          itemCount: bannerViewList.length,
          itemBuilder: (BuildContext context, index, int pageViewIndex) =>
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 25),
                child: InkWell(
                  onTap: () {
                    Get.to(() => CafeListScreen());
                  },
                  child: Container(
                    height: 135,
                    width: Get.width,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      image: DecorationImage(
                        image: AssetImage(bannerViewList[index]["image"]),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
              ),
          options: CarouselOptions(
              height: 180,
              autoPlay: true,
              autoPlayAnimationDuration: Duration(milliseconds: 800),
              enlargeCenterPage: true,
              scrollDirection: Axis.horizontal,
              initialPage: 3,
              viewportFraction: 0.99,
              onPageChanged: (index, reason) {}),
        ),
      ],
    );
  }
}