import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';

import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class ReservationDone extends StatelessWidget {
  const ReservationDone({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      body: ScrollConfiguration(
        behavior: MyBehavior(),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: Get.width,
                color: greyF5F,
                child: Column(
                  children: [
                    SizedBox(height: 60),
                    Padding(
                      padding: EdgeInsets.only(left: 25, right: 25, bottom: 44),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InkWell(
                            onTap: () {
                              Get.back();
                            },
                            child: Icon(
                              Icons.arrow_back,
                              color: black2D2,
                              size: 24,
                            ),
                          ),
                          Row(
                            children: [
                              SvgPicture.asset(Images.microphoneBlankIcon,
                                  color: black2D2),
                              SizedBox(width: 30),
                              SvgPicture.asset(Images.arrowShareIcon,
                                  color: black2D2),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Image.asset(Images.burger, height: 60, width: 60),
                    SizedBox(height: 15),
                    CommonTextWidget.PoppinsMedium(
                      text: "Afiyet Olsun",
                      fontSize: 20,
                      color: black2D2,
                    ),
                    SizedBox(height: 5),
                    CommonTextWidget.PoppinsRegular(
                      color: grey8D8,
                      fontSize: 16,
                      text: "Rezervasyon ID: 5045010",
                    ),
                    SizedBox(height: 50),
                  ],
                ),
              ),
              Padding(
                padding:
                EdgeInsets.only(top: 15, left: 25, right: 25, bottom: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CommonTextWidget.PoppinsMedium(
                            text: "Nusr-Et Etiler",
                            fontSize: 18,
                            color: black2D2,
                          ),
                          CommonTextWidget.PoppinsRegular(
                            text:
                            "Nispetiye Caddesi No:87, Etiler, Beşiktaş, İstanbul",
                            fontSize: 12,
                            color: greyA3A,
                          ),
                        ],
                      ),
                    ),
                    SvgPicture.asset(Images.circuleMapPin),
                  ],
                ),
              ),
              Container(
                width: Get.width,
                color: yellowF9B.withOpacity(0.15),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30, vertical: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        children: [
                          CommonTextWidget.PoppinsRegular(
                            text: "Tarih",
                            fontSize: 12,
                            color: grey545,
                          ),
                          SizedBox(height: 6),
                          CommonTextWidget.PoppinsMedium(
                            text: "Cum, 01 Eki",
                            fontSize: 14,
                            color: black2D2,
                          ),
                        ],
                      ),
                      Container(
                        height: 80,
                        padding: EdgeInsets.all(10),
                        child: VerticalDivider(
                          color: black.withOpacity(0.15),
                          thickness: 1,
                          indent: 0,
                          endIndent: 0,
                          width: 20,
                        ),
                      ),
                      Column(
                        children: [
                          CommonTextWidget.PoppinsRegular(
                            text: "Saat",
                            fontSize: 12,
                            color: grey545,
                          ),
                          SizedBox(height: 6),
                          CommonTextWidget.PoppinsMedium(
                            text: "19:00",
                            fontSize: 14,
                            color: black2D2,
                          ),
                        ],
                      ),
                      Container(
                        height: 80,
                        padding: EdgeInsets.all(10),
                        child: VerticalDivider(
                          color: black.withOpacity(0.15),
                          thickness: 1,
                          indent: 0,
                          endIndent: 0,
                          width: 20,
                        ),
                      ),
                      Column(
                        children: [
                          CommonTextWidget.PoppinsRegular(
                            text: "Misafirler",
                            fontSize: 12,
                            color: grey545,
                          ),
                          SizedBox(height: 6),
                          CommonTextWidget.PoppinsMedium(
                            text: "2",
                            fontSize: 14,
                            color: black2D2,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 15),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Divider(thickness: 1, color: greyD4D),
              ),
              ListTile(
                contentPadding: EdgeInsets.symmetric(horizontal: 25),
                leading: SvgPicture.asset(Images.notePad),
                title: CommonTextWidget.PoppinsMedium(
                  fontSize: 14,
                  text: "Yemek Menüsü",
                  color: black2D2,
                ),
                subtitle: CommonTextWidget.PoppinsRegular(
                  color: black616,
                  fontSize: 10,
                  text: "Temassız yemek için",
                ),
                trailing: MaterialButton(
                  onPressed: () {},
                  height: 38,
                  minWidth: 109,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  color: yellowF9B,
                  child: CommonTextWidget.PoppinsMedium(
                    fontSize: 15,
                    text: "Şimdi Gör",
                    color: white,
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Divider(thickness: 1, color: greyD4D),
              ),
              Padding(
                padding:
                EdgeInsets.only(top: 20, bottom: 15, left: 25, right: 25),
                child: CommonTextWidget.PoppinsMedium(
                  fontSize: 18,
                  text: "Seçili Kampanya",
                  color: black2D2,
                ),
              ),
              Padding(
                padding: EdgeInsets.only(bottom: 10, left: 25, right: 25),
                child: CommonTextWidget.PoppinsRegular(
                  fontSize: 14,
                  text: "Uygulamada ödeme yaparak %40'a kadar indirim kazanın.",
                  color: black2D2,
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 25, right: 25, bottom: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CommonTextWidget.PoppinsRegular(
                      fontSize: 14,
                      text: "Şartlar ve Koşullar",
                      color: black616,
                    ),
                    Row(
                      children: [
                        CommonTextWidget.PoppinsRegular(
                          fontSize: 14,
                          text: "Gizle",
                          color: yellowF9B,
                        ),
                        SizedBox(width: 7),
                        SvgPicture.asset(Images.arrowUpIcon),
                      ],
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 25, right: 25, bottom: 10),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonTextWidget.PoppinsRegular(
                      fontSize: 12,
                      text: "\u2022 ",
                      color: black616,
                    ),
                    Expanded(
                      child: CommonTextWidget.PoppinsRegular(
                        fontSize: 12,
                        text: "Başka bir kampanya ile birleştirilemez.",
                        color: black616,
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 25, right: 25, bottom: 10),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonTextWidget.PoppinsRegular(
                      fontSize: 12,
                      text: "\u2022 ",
                      color: black616,
                    ),
                    Expanded(
                      child: CommonTextWidget.PoppinsRegular(
                        fontSize: 12,
                        text: "Kampanyalar sadece önceden rezervasyon yapılması durumunda geçerlidir.",
                        color: black616,
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: 25, right: 25, bottom: 10),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonTextWidget.PoppinsRegular(
                      fontSize: 12,
                      text: "\u2022 ",
                      color: black616,
                    ),
                    Expanded(
                      child: CommonTextWidget.PoppinsRegular(
                        fontSize: 12,
                        text: "Bazı kampanyalar resmi tatillerde geçerli olmayabilir.",
                        color: black616,
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 12, vertical: 20),
                child: Divider(thickness: 1, color: greyD4D),
              ),
              Center(
                child: CommonTextWidget.PoppinsMedium(
                  fontSize: 14,
                  text: "Sorularınız için bize ulaşın:",
                  color: black616,
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(height: 15),
              Center(
                child: CommonTextWidget.PoppinsMedium(
                  fontSize: 15,
                  text: "+90 212 345 6789",
                  color: yellowF9B,
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(height: 30),
            ],
          ),
        ),
      ),
    );
  }
}
