import 'package:carousel_slider/carousel_slider.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Controllers/cafe_detail_controller.dart';
import 'package:fastyorderapp/Screens/HomeScreen/reservation_screen.dart';
import 'package:fastyorderapp/Screens/MenuScreen/menu_screen.dart';
import 'package:fastyorderapp/Screens/userreview/user_review_screen.dart';
import 'package:fastyorderapp/Utills/common_button_widget.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/main.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

import 'favorite.dart';

class FirmDetailScreen extends StatelessWidget {
  FirmDetailScreen({Key? key, this.firmId})
      : super(key: key); // required kaldırıldı

  final String? firmId; // nullable yapıldı
  final FavoriteService _favoriteService = FavoriteService(); // Ekle

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final CafeDetailSliderController cafeDetailSliderController =
      Get.put(CafeDetailSliderController());

  final List<Map> sliderImageList = [
    {"image": Images.cafeDetailImage},
    {"image": Images.cafeDetailImage},
    {"image": Images.cafeDetailImage},
    {"image": Images.cafeDetailImage},
    {"image": Images.cafeDetailImage},
    {"image": Images.cafeDetailImage},
  ];

  @override
  Widget build(BuildContext context) {
    if (firmId == null) {
      return const Scaffold(
        body: Center(child: Text('Firma ID bulunamadı')),
      );
    }
    if (firmId != null) {
      _favoriteService.checkIfFavorited(firmId!);
    }
    return StreamBuilder<DocumentSnapshot>(
      stream: _firestore.collection('firms').doc(firmId).snapshots(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return const Scaffold(
            body: Center(child: Text('Bir hata oluştu')),
          );
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: CircularProgressIndicator()),
          );
        }

        final data = snapshot.data?.data() as Map<String, dynamic>?;
        if (data == null) {
          return const Scaffold(
            body: Center(child: Text('Firma bulunamadı')),
          );
        }

        // Geri kalan kod aynı...
        return Scaffold(
          backgroundColor: white,
          body: Stack(
            alignment: Alignment.bottomCenter,
            children: [
              ScrollConfiguration(
                behavior: MyBehavior(),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Image Slider Section
                      Stack(
                        alignment: Alignment.bottomCenter,
                        children: [
                          CarouselSlider.builder(
                            itemCount: sliderImageList.length,
                            itemBuilder:
                                (BuildContext context, index, pageViewIndex) =>
                                    Container(
                              height: 250,
                              width: Get.width,
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: AssetImage(
                                      sliderImageList[index]["image"]),
                                  fit: BoxFit.fill,
                                ),
                              ),
                            ),
                            options: CarouselOptions(
                              height: 250,
                              autoPlay: true,
                              autoPlayAnimationDuration:
                                  Duration(milliseconds: 800),
                              scrollDirection: Axis.horizontal,
                              viewportFraction: 1.0,
                              aspectRatio: 2.0,
                              onPageChanged: (index, reason) {
                                cafeDetailSliderController
                                    .sliderIndexCafeDetail.value = index;
                              },
                            ),
                          ),

                          // Back and Share Buttons
                          Padding(
                            padding: EdgeInsets.only(
                                left: 25, right: 25, bottom: 190),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                InkWell(
                                  onTap: () => Get.back(),
                                  child: Icon(Icons.arrow_back,
                                      color: white, size: 24),
                                ),
                                Row(
                                  children: [
                                    SvgPicture.asset(Images.arrowShareIcon),
                                    SizedBox(width: 30),
                                    Icon(Icons.favorite_border,
                                        size: 24, color: white),
                                  ],
                                ),
                              ],
                            ),
                          ),

                          // Slider Indicators
                          Obx(
                            () => Padding(
                              padding: EdgeInsets.only(bottom: 10),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  for (int i = 0;
                                      i < sliderImageList.length;
                                      i++)
                                    Padding(
                                      padding: EdgeInsets.all(6),
                                      child: Container(
                                        height: 10,
                                        width: 10,
                                        decoration: BoxDecoration(
                                          color: i ==
                                                  cafeDetailSliderController
                                                      .sliderIndexCafeDetail
                                                      .value
                                              ? yellowF9B
                                              : white.withOpacity(0.4),
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),

                      // Restaurant Info Section
                      Padding(
                        padding:
                            EdgeInsets.symmetric(horizontal: 25, vertical: 15),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        CommonTextWidget.PoppinsMedium(
                                          text: data['name'] ?? '',
                                          fontSize: 18,
                                          color: black2D2,
                                        ),
                                        SizedBox(width: 8),
                                        Container(
                                          height: 18,
                                          width: 32,
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(3),
                                            color: green55A,
                                          ),
                                          child: Center(
                                            child:
                                                CommonTextWidget.PoppinsMedium(
                                              text: "4.5",
                                              fontSize: 10,
                                              color: white,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    CommonTextWidget.PoppinsRegular(
                                      text:
                                          "${data['district']}, ${data['city']}",
                                      fontSize: 12,
                                      color: greyA3A,
                                    ),
                                  ],
                                ),
                                Row(
                                  children: [
                                    InkWell(
                                      onTap: () => _favoriteService
                                          .toggleFavorite(firmId!),
                                      child: Obx(() => Icon(
                                            _favoriteService.isFavorite.value
                                                ? Icons.favorite
                                                : Icons.favorite_border,
                                            size: 24,
                                            color: _favoriteService
                                                    .isFavorite.value
                                                ? yellowF9B
                                                : black2D2,
                                          )),
                                    ),
                                    SizedBox(width: 15),
                                    SvgPicture.asset(Images.phoneCallImage),
                                  ],
                                ),
                              ],
                            ),
                            SizedBox(height: 10),
                            CommonTextWidget.PoppinsRegular(
                              text: (data['categories'] as List?)?.join(', ') ??
                                  '',
                              fontSize: 11,
                              color: black2D2,
                            ),
                          ],
                        ),
                      ),
                      // Divider
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 12),
                        child: Divider(thickness: 1, color: greyD4D),
                      ),

                      // Top Insider Tips Section
                      ListTile(
                        title: CommonTextWidget.PoppinsMedium(
                          text: "Özellikler",
                          color: black2D2,
                          fontSize: 14,
                        ),
                        subtitle: CommonTextWidget.PoppinsRegular(
                          text: (data['reservationDescriptions'] as List?)
                                  ?.join(' | ') ??
                              '',
                          color: black616,
                          fontSize: 12,
                        ),
                        trailing: Image.asset(Images.bulbImage,
                            height: 35, width: 35),
                      ),

                      // Divider
                      Padding(
                        padding: EdgeInsets.only(left: 25, right: 25),
                        child: Divider(thickness: 1, color: greyECE),
                      ),

                      // Tip Description
// Tip Description
                      Padding(
                        padding: EdgeInsets.only(
                            top: 15, bottom: 10, left: 25, right: 25),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CommonTextWidget.PoppinsRegular(
                              fontSize: 12,
                              color: black616,
                              text: " \u2022  ",
                            ),
                            Expanded(
                              child: CommonTextWidget.PoppinsRegular(
                                fontSize: 12,
                                color: black616,
                                text: (data['reservationDescriptions'] as List?)
                                        ?.first ??
                                    'Açıklama bulunamadı', // Liste'nin ilk elemanını alıyoruz
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Restaurant Information
                      Padding(
                        padding: EdgeInsets.only(
                            top: 15, bottom: 10, left: 25, right: 25),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SvgPicture.asset(Images.money),
                            SizedBox(width: 10),
                            Expanded(
                              child: CommonTextWidget.PoppinsRegular(
                                fontSize: 12,
                                color: black616,
                                text: "₺${data['minReservationAmount'] ?? ''}",
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Features List
                      Padding(
                        padding:
                            EdgeInsets.only(left: 25, right: 25, bottom: 15),
                        child: CommonTextWidget.PoppinsMedium(
                          text: "Özellikler",
                          color: black2D2,
                          fontSize: 14,
                        ),
                      ),

                      _buildFeatureItem(Images.truck, "Rezervasyon"),
                      _buildFeatureItem(Images.moon, "Gece Hayatı"),
                      _buildFeatureItem(Images.backpack, "Gel-Al"),
                      _buildFeatureItem(
                          Images.wheelchair, "Tekerlekli Sandalye Erişimi"),

                      // Ratings Section
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 25),
                        child: CommonTextWidget.PoppinsMedium(
                          fontSize: 14,
                          color: black2D2,
                          text: "Kullanıcı Puanı ve Yorum Özeti",
                        ),
                      ),

                      // Rating Stats
                      SizedBox(height: 10),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 25),
                        child: Row(
                          children: [
                            CommonTextWidget.PoppinsMedium(
                              fontSize: 36,
                              color: black2D2,
                              text: "4.5",
                            ),
                            SizedBox(width: 8),
                            CommonTextWidget.PoppinsRegular(
                              fontSize: 12,
                              color: black2D2,
                              text: "(579 yorum)",
                            ),
                          ],
                        ),
                      ),

                      // Rating Bar
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 25),
                        child: RatingBar.builder(
                          initialRating: 5,
                          minRating: 5,
                          direction: Axis.horizontal,
                          allowHalfRating: true,
                          itemCount: 5,
                          ignoreGestures: true,
                          itemSize: 21,
                          itemPadding: EdgeInsets.symmetric(horizontal: 4.0),
                          itemBuilder: (context, _) => Icon(
                            Icons.star,
                            color: Colors.amber,
                          ),
                          onRatingUpdate: (rating) {},
                        ),
                      ),

                      // Detailed Ratings
                      SizedBox(height: 20),
                      _buildDetailedRatings(),

                      // Review Buttons
                      SizedBox(height: 35),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 25),
                        child: CommonButtonWidget.button(
                          text: "Değerlendir",
                          onTap: () {},
                          buttonColor: yellowF9B,
                        ),
                      ),
                      SizedBox(height: 15),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 25),
                        child: MaterialButton(
                          onPressed: () => Get.to(() => UserReviewScreen()),
                          height: 45,
                          minWidth: Get.width,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                            side: BorderSide(color: yellowF9B, width: 1.5),
                          ),
                          color: white,
                          child: CommonTextWidget.PoppinsMedium(
                            fontSize: 15,
                            text: "Değerlendirmeleri Gör",
                            color: yellowF9B,
                          ),
                        ),
                      ),
                      SizedBox(height: 100),
                    ],
                  ),
                ),
              ),

              // Bottom Buttons
              Container(
                decoration: BoxDecoration(
                  color: white,
                  boxShadow: [
                    BoxShadow(
                      color: black.withOpacity(0.08),
                      spreadRadius: 0,
                      offset: Offset(0, -4),
                      blurRadius: 16,
                    ),
                  ],
                ),
                child: Padding(
                  padding:
                      EdgeInsets.only(top: 15, bottom: 35, left: 25, right: 25),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: CommonButtonWidget.button(
                          text: "Rezervasyon Yap",
                          buttonColor: yellowF9B,
                          onTap: () => Get.to(() => ReservationScreen()),
                        ),
                      ),
                      SizedBox(width: 10),
                      Expanded(
                        child: CommonButtonWidget.button(
                          text: "Gel-Al Siparişi Ver",
                          buttonColor: yellowF9B,
                          onTap: () => Get.to(() => MenuScreen()),
                        ),
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }

  Widget _buildFeatureItem(String icon, String text) {
    return Padding(
      padding: EdgeInsets.only(top: 5, bottom: 10, left: 25, right: 25),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SvgPicture.asset(icon),
          SizedBox(width: 10),
          Expanded(
            child: CommonTextWidget.PoppinsRegular(
              fontSize: 12,
              color: black616,
              text: text,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedRatings() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 25),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Rating Categories
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildRatingLabel("Ambiyans"),
              _buildRatingLabel("Yemek"),
              _buildRatingLabel("Hijyen"),
              _buildRatingLabel("Ses"),
              _buildRatingLabel("Fiyatlandırma"),
              _buildRatingLabel("Hizmet"),
              _buildRatingLabel("Çeşitlilik"),
            ],
          ),
          // Progress Bars
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildProgressBar(0.8),
              _buildProgressBar(0.8),
              _buildProgressBar(0.9),
              _buildProgressBar(0.9),
              _buildProgressBar(0.9),
              _buildProgressBar(0.6),
              _buildProgressBar(0.2, isRed: true),
            ],
          ),
          // Rating Values
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildRatingValue("4.7"),
              _buildRatingValue("4.7"),
              _buildRatingValue("4.8"),
              _buildRatingValue("4.8"),
              _buildRatingValue("4.8"),
              _buildRatingValue("4.6"),
              _buildRatingValue("2.8"),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRatingLabel(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 15),
      child: CommonTextWidget.PoppinsMedium(
        text: text,
        color: black2D2,
        fontSize: 12,
      ),
    );
  }

  Widget _buildRatingValue(String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 15),
      child: CommonTextWidget.PoppinsMedium(
        text: value,
        color: black2D2,
        fontSize: 12,
      ),
    );
  }

  Widget _buildProgressBar(double percent, {bool isRed = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 20),
      child: LinearPercentIndicator(
        width: 225,
        animation: true,
        animationDuration: 1000,
        lineHeight: 10,
        backgroundColor: greyE5E,
        percent: percent,
        progressColor: isRed ? redF03 : yellowF9B,
      ),
    );
  }
}
