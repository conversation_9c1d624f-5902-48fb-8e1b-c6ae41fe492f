import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/font_family.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/main.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class SearchScreen extends StatelessWidget {
  SearchScreen({Key? key}) : super(key: key);
  final TextEditingController searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        backgroundColor: white,
        automaticallyImplyLeading: false,
        centerTitle: true,
        elevation: 0,
        leading: Padding(
          padding: EdgeInsets.only(left: 20),
          child: InkWell(
            onTap: () {
              Get.back();
            },
            child: Icon(
              Icons.arrow_back,
              color: black2D2,
              size: 24,
            ),
          ),
        ),
        title: CommonTextWidget.PoppinsMedium(
          text: "Arama",
          fontSize: 18,
          color: black2D2,
        ),
      ),
      body: ScrollConfiguration(
        behavior: MyBehavior(),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /// TextField Widget View
              TextFieldWidgetView(),

              /// Top searched restaurants Widget View
              TopSearchedRestaurantsWidgetView(),

              /// Most booked restaurants Widget View
              MostBookedRestaurantsWidgetView(),
            ],
          ),
        ),
      ),
    );
  }

  /// TextField Widget View
  Widget TextFieldWidgetView() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 25, vertical: 25),
      child: TextFormField(
        keyboardType: TextInputType.text,
        cursorColor: black2D2,
        controller: searchController,
        style: TextStyle(
          color: black2D2,
          fontSize: 14,
          fontFamily: FontFamily.PoppinsRegular,
        ),
        decoration: InputDecoration(
          hintText: "Restoranları, konumları ara...",
          hintStyle: TextStyle(
            color: grey9C9,
            fontSize: 14,
            fontFamily: FontFamily.PoppinsRegular,
          ),
          prefixIcon: Icon(
            CupertinoIcons.search,
            color: black2D2,
            size: 20,
          ),

          filled: true,
          fillColor: white,
          contentPadding: EdgeInsets.zero,
          border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: grey9C9, width: 1)),
          focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: grey9C9, width: 1)),
          enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: grey9C9, width: 1)),
          errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: grey9C9, width: 1)),
        ),
      ),
    );
  }

  /// Top searched restaurants Widget View
  Widget TopSearchedRestaurantsWidgetView() {
    final FirebaseFirestore _firestore = FirebaseFirestore.instance;
    return StreamBuilder<QuerySnapshot>(
      stream: _firestore.collection('firms').orderBy('searchCount', descending: true).limit(5).snapshots(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Center(child: Text('Bir hata oluştu'));
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(child: CircularProgressIndicator());
        }

        return Padding(
          padding: EdgeInsets.only(top: 5, left: 25, right: 25),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CommonTextWidget.PoppinsMedium(
                text: "En çok aranan restoranlar",
                fontSize: 16,
                color: black2D2,
              ),
              SizedBox(height: 20),
              ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: snapshot.data!.docs.length,
                padding: EdgeInsets.zero,
                itemBuilder: (context, index) {
                  final doc = snapshot.data!.docs[index];
                  final data = doc.data() as Map<String, dynamic>;

                  return Padding(
                    padding: EdgeInsets.only(bottom: 20),
                    child: Row(
                      children: [
                        Container(
                          height: 75,
                          width: 75,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            image: DecorationImage(
                              image: NetworkImage(data['image'] ?? ''),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        SizedBox(width: 12),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CommonTextWidget.PoppinsMedium(
                              text: data['name'] ?? '',
                              fontSize: 14,
                              color: black2D2,
                            ),
                            CommonTextWidget.PoppinsLight(
                              text: data['location'] ?? '',
                              fontSize: 10,
                              color: black2D2,
                            ),
                            CommonTextWidget.PoppinsMedium(
                              text: data['discount'] ?? '',
                              fontSize: 12,
                              color: yellowF9B,
                            ),
                          ],
                        ),
                        Expanded(
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: Container(
                              height: 18,
                              width: 32,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                color: yellowF9B,
                              ),
                              child: Center(
                                child: CommonTextWidget.PoppinsMedium(
                                  text: data['rating']?.toString() ?? '',
                                  fontSize: 12,
                                  color: white,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// Most booked restaurants Widget View
  Widget MostBookedRestaurantsWidgetView() {
    final FirebaseFirestore _firestore = FirebaseFirestore.instance;
    return StreamBuilder<QuerySnapshot>(
      stream: _firestore.collection('firms').orderBy('bookingCount', descending: true).limit(5).snapshots(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Center(child: Text('Bir hata oluştu'));
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(child: CircularProgressIndicator());
        }

        return Padding(
          padding: EdgeInsets.only(top: 5, left: 25, right: 25),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CommonTextWidget.PoppinsMedium(
                text: "En çok rezerve edilen restoranlar",
                fontSize: 16,
                color: black2D2,
              ),
              SizedBox(height: 20),
              ListView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                itemCount: snapshot.data!.docs.length,
                padding: EdgeInsets.zero,
                itemBuilder: (context, index) {
                  final doc = snapshot.data!.docs[index];
                  final data = doc.data() as Map<String, dynamic>;

                  return Padding(
                    padding: EdgeInsets.only(bottom: 20),
                    child: Row(
                      children: [
                        Container(
                          height: 75,
                          width: 75,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            image: DecorationImage(
                              image: NetworkImage(data['image'] ?? ''),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        SizedBox(width: 12),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CommonTextWidget.PoppinsMedium(
                              text: data['name'] ?? '',
                              fontSize: 14,
                              color: black2D2,
                            ),
                            CommonTextWidget.PoppinsLight(
                              text: data['location'] ?? '',
                              fontSize: 10,
                              color: black2D2,
                            ),
                            CommonTextWidget.PoppinsMedium(
                              text: data['discount'] ?? '',
                              fontSize: 12,
                              color: yellowF9B,
                            ),
                          ],
                        ),
                        Expanded(
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: Container(
                              height: 18,
                              width: 32,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                color: yellowF9B,
                              ),
                              child: Center(
                                child: CommonTextWidget.PoppinsMedium(
                                  text: data['rating']?.toString() ?? '',
                                  fontSize: 12,
                                  color: white,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        );
      },
    );
  }
}