import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Utills/common_button_widget.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';

import '../../main.dart';
import '../PaymentScreen/credit_card_screen.dart';

class ReservationScreen extends StatefulWidget {
  const ReservationScreen({Key? key}) : super(key: key);

  @override
  _ReservationScreenState createState() => _ReservationScreenState();
}

class _ReservationScreenState extends State<ReservationScreen> {
  DateTime? selectedDate;
  TimeOfDay? selectedTime;
  int selectedGuests = 1;
  final TextEditingController notesController = TextEditingController();

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(Duration(days: 3)),
      builder: (BuildContext context, Widget? child) {
        return Theme(
          data: ThemeData.light().copyWith(
            colorScheme: ColorScheme.light(
              primary: yellowF9B,
              onPrimary: white,
              surface: white,
              onSurface: black2D2,
            ),
            dialogBackgroundColor: white,
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != selectedDate)
      setState(() {
        selectedDate = picked;
      });
  }

  Future<void> _selectTime(BuildContext context) async {
    final List<TimeOfDay> availableTimes = List.generate(48, (index) {
      final int hour = index ~/ 2;
      final int minute = (index % 2) * 30;
      return TimeOfDay(hour: hour, minute: minute);
    }).where((time) {
      if (selectedDate == null) return false;
      final DateTime nowDateTime = DateTime.now();
      final DateTime selectedDateTime = DateTime(
        selectedDate!.year,
        selectedDate!.month,
        selectedDate!.day,
        time.hour,
        time.minute,
      );
      if (selectedDate!.day == nowDateTime.day) {
        return selectedDateTime.isAfter(nowDateTime.add(Duration(hours: 2)));
      }
      return true;
    }).toList();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("Saat Seçin"),
          content: Container(
            width: double.minPositive,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: availableTimes.length,
              itemBuilder: (BuildContext context, int index) {
                final time = availableTimes[index];
                return ListTile(
                  title: Text(
                      "${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}"),
                  onTap: () {
                    setState(() {
                      selectedTime = time;
                    });
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  Future<void> _selectGuests(BuildContext context) async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text("Kişi Sayısı Seç"),
          content: Container(
            width: double.minPositive,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: 10,
              itemBuilder: (BuildContext context, int index) {
                final int guests = index + 1;
                return ListTile(
                  title: Text(guests.toString()),
                  onTap: () {
                    setState(() {
                      selectedGuests = guests;
                    });
                    Navigator.of(context).pop();
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      body: ScrollConfiguration(
        behavior: MyBehavior(),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 60),
                Row(
                  children: [
                    InkWell(
                      onTap: () {
                        Get.back();
                      },
                      child: Icon(
                        Icons.arrow_back,
                        color: black2D2,
                        size: 24,
                      ),
                    ),
                    SizedBox(width: 10),
                    CommonTextWidget.PoppinsMedium(
                      text: "Rezervasyon Yap",
                      fontSize: 20,
                      color: black2D2,
                    ),
                  ],
                ),
                SizedBox(height: 20),
                Text(
                  "Tarih Seç",
                  style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'PoppinsMedium',
                      color: black2D2),
                ),
                SizedBox(height: 10),
                InkWell(
                  onTap: () => _selectDate(context),
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 15, horizontal: 10),
                    decoration: BoxDecoration(
                      border: Border.all(color: greyD4D),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          selectedDate == null
                              ? "Tarih Seçin"
                              : "${selectedDate!.day}/${selectedDate!.month}/${selectedDate!.year}",
                          style: TextStyle(
                              color: greyA3A, fontFamily: 'PoppinsRegular'),
                        ),
                        Icon(Icons.calendar_today, color: greyA3A),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 20),
                Text(
                  "Saat Seç",
                  style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'PoppinsMedium',
                      color: black2D2),
                ),
                SizedBox(height: 10),
                InkWell(
                  onTap: () => _selectTime(context),
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 15, horizontal: 10),
                    decoration: BoxDecoration(
                      border: Border.all(color: greyD4D),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          selectedTime == null
                              ? "Saat Seçin"
                              : "${selectedTime!.hour}:${selectedTime!.minute.toString().padLeft(2, '0')}",
                          style: TextStyle(
                              color: greyA3A, fontFamily: 'PoppinsRegular'),
                        ),
                        Icon(Icons.access_time, color: greyA3A),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 20),
                Text(
                  "Kişi Sayısı Seç",
                  style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'PoppinsMedium',
                      color: black2D2),
                ),
                SizedBox(height: 10),
                InkWell(
                  onTap: () => _selectGuests(context),
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 15, horizontal: 10),
                    decoration: BoxDecoration(
                      border: Border.all(color: greyD4D),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "$selectedGuests Kişi",
                          style: TextStyle(
                              color: greyA3A, fontFamily: 'PoppinsRegular'),
                        ),
                        Icon(Icons.people, color: greyA3A),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 20),
                Text(
                  "Rezervasyon Notları",
                  style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'PoppinsMedium',
                      color: black2D2),
                ),
                SizedBox(height: 10),
                TextField(
                  controller: notesController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(),
                    hintText: "Özel istek veya not ekleyin",
                    hintStyle: TextStyle(fontFamily: 'PoppinsRegular'),
                  ),
                ),
                SizedBox(height: 30),
                Center(
                  child: CommonButtonWidget.button(
                    text: "Rezervasyon Yap",
                    buttonColor: yellowF9B,
                    onTap: () {
                      Get.to(() => PaymentScreen());
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
