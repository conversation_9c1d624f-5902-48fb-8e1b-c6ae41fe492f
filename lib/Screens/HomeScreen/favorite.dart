// lib/Services/favorite_service.dart

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';

class FavoriteService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // Favori durumunu kontrol eden RxBool
  final RxBool isFavorite = false.obs;

  // Favori durumunu kontrol et
  Future<void> checkIfFavorited(String firmId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    try {
      final userDoc = await _firestore
          .collection('users')
          .doc(currentUser.uid)
          .get();

      if (userDoc.exists) {
        final favoritedFirms = List<String>.from(userDoc.data()?['favoritedFirms'] ?? []);
        isFavorite.value = favoritedFirms.contains(firmId);
      }
    } catch (e) {
      print('Favori kontrolü hatası: $e');
    }
  }

  // Favori durumunu değiştir
  Future<void> toggleFavorite(String firmId) async {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return;

    try {
      final userRef = _firestore.collection('users').doc(currentUser.uid);
      final userDoc = await userRef.get();

      if (userDoc.exists) {
        final favoritedFirms = List<String>.from(userDoc.data()?['favoritedFirms'] ?? []);

        if (isFavorite.value) {
          favoritedFirms.remove(firmId);
        } else {
          favoritedFirms.add(firmId);
        }

        await userRef.update({'favoritedFirms': favoritedFirms});
        isFavorite.toggle();
      } else {
        // Kullanıcı dokümanı yoksa oluştur
        await userRef.set({
          'favoritedFirms': [firmId],
          'createdAt': FieldValue.serverTimestamp(),
        });
        isFavorite.value = true;
      }
    } catch (e) {
      print('Favorileme hatası: $e');
    }
  }

  // Favorileri getir
  Stream<List<String>> getFavorites() {
    final currentUser = _auth.currentUser;
    if (currentUser == null) return Stream.value([]);

    return _firestore
        .collection('users')
        .doc(currentUser.uid)
        .snapshots()
        .map((doc) {
      if (!doc.exists) return [];
      return List<String>.from(doc.data()?['favoritedFirms'] ?? []);
    });
  }
}