import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Utills/common_button_widget.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/main.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CancelBookingScreen extends StatelessWidget {
  CancelBookingScreen({Key? key}) : super(key: key);

  final List<Map> cancelBookingList = [
    {
      "image": Images.cancelBooking1,
      "text1": "<PERSON><PERSON>",
      "text2": "İstiklal Caddesi, Beyoğlu",
    },
    {
      "image": Images.cancelBooking2,
      "text1": "Simit Sarayı",
      "text2": "Kadıköy, İstanbul",
    },
    {
      "image": Images.cancelBooking3,
      "text1": "Karaköy Güllüoğlu",
      "text2": "Karaköy, İstanbul",
    },
    {
      "image": Images.cancelBooking4,
      "text1": "<PERSON><PERSON>",
      "text2": "<PERSON>lor<PERSON>, İstanbul",
    },
    {
      "image": Images.cancelBooking5,
      "text1": "<PERSON><PERSON><PERSON> Çınaraltı",
      "text2": "Çengelköy, İstanbul",
    },
    {
      "image": Images.cancelBooking6,
      "text1": "Künefeci Yusuf Usta",
      "text2": "Fatih, İstanbul",
    },
    {
      "image": Images.cancelBooking7,
      "text1": "Nusr-Et Steakhouse",
      "text2": "Etiler, İstanbul",
    },
    {
      "image": Images.cancelBooking8,
      "text1": "Bursa Kebapçısı",
      "text2": "Taksim, İstanbul",
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 145),
      child: Container(
        height: Get.height,
        width: Get.width,
        color: white,
        child: ScrollConfiguration(
          behavior: MyBehavior(),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 25, vertical: 20),
                  child: CommonTextWidget.PoppinsMedium(
                    text: "Rezervasyonu İptal Et?",
                    color: black2D2,
                    fontSize: 18,
                  ),
                ),
                Container(
                  width: Get.width,
                  color: yellowFEF,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 25, vertical: 20),
                    child: Row(
                      children: [
                        Image.asset(Images.salaryImage, height: 35, width: 35),
                        SizedBox(width: 10),
                        Expanded(
                          child: CommonTextWidget.PoppinsMedium(
                            fontSize: 14,
                            text:
                            "Bu iptal ile 50 EazyPuan kaybedeceksiniz.",
                            color: black2D2,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 25, right: 25, top: 20),
                  child: CommonTextWidget.PoppinsRegular(
                    text: "Bu restoranı rezerve eden diğer müşteriler de...",
                    color: black2D2,
                    fontSize: 13,
                  ),
                ),
                SizedBox(
                  height: 315,
                  width: Get.width,
                  child: ScrollConfiguration(
                    behavior: MyBehavior(),
                    child: ListView.builder(
                      itemCount: cancelBookingList.length,
                      padding: EdgeInsets.only(
                        left: 25,
                        right: 5,
                        top: 15,
                        bottom: 15,
                      ),
                      scrollDirection: Axis.horizontal,
                      shrinkWrap: true,
                      itemBuilder: (context, index) => Padding(
                        padding: EdgeInsets.only(right: 20),
                        child: Container(
                          height: 315,
                          width: 200,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: white,
                            boxShadow: [
                              BoxShadow(
                                color: black2D2.withOpacity(0.08),
                                spreadRadius: 0,
                                offset: Offset(0, 0),
                                blurRadius: 16,
                              ),
                            ],
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                height: 180,
                                width: 200,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.only(
                                    topRight: Radius.circular(10),
                                    topLeft: Radius.circular(10),
                                  ),
                                  image: DecorationImage(
                                    image: AssetImage(
                                      cancelBookingList[index]["image"],
                                    ),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                    left: 12, right: 12, top: 10),
                                child: CommonTextWidget.PoppinsMedium(
                                  text: cancelBookingList[index]["text1"],
                                  color: black2D2,
                                  fontSize: 16,
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                    left: 12, right: 12, bottom: 10),
                                child: CommonTextWidget.PoppinsRegular(
                                  text: cancelBookingList[index]["text2"],
                                  color: grey9C9,
                                  fontSize: 12,
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.only(
                                    left: 12, right: 12, bottom: 20),
                                child: CommonTextWidget.PoppinsSemiBold(
                                  text:
                                  "Uygulamada ödeyin ve %40'a kadar indirim kazanın",
                                  color: yellowF9B,
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 25, right: 25, bottom: 18),
                  child: CommonTextWidget.PoppinsRegular(
                    text:
                    "Mevcut 'Masala Library' rezervasyonunu 12:00'de iptal etmek istiyor musunuz?",
                    color: black2D2,
                    fontSize: 13,
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 25, right: 25, bottom: 40),
                  child: Row(
                    children: [
                      Expanded(
                        child: CommonButtonWidget.button(
                          onTap: () {},
                          buttonColor: redFB4,
                          text: "Rezervasyonu İptal Et",
                        ),
                      ),
                      SizedBox(width: 15),
                      Expanded(
                        child: CommonButtonWidget.button(
                          onTap: () {},
                          buttonColor: yellowF9B,
                          text: "İptal Etme",
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
