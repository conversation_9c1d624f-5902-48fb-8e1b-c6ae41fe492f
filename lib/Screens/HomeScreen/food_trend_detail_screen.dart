import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/main.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class FoodTrendDetailScreen extends StatelessWidget {
  const FoodTrendDetailScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      appBar: AppBar(
        backgroundColor: white,
        automaticallyImplyLeading: false,
        centerTitle: true,
        elevation: 0,
        leading: Padding(
          padding: EdgeInsets.only(left: 20),
          child: InkWell(
            onTap: () {
              Get.back();
            },
            child: Icon(
              Icons.arrow_back,
              color: black2D2,
              size: 24,
            ),
          ),
        ),
        title: CommonTextWidget.PoppinsMedium(
          text: "Food Trends",
          fontSize: 18,
          color: black2D2,
        ),
      ),
      body: BodyWidgetView(),
    );
  }

  /// Body Widget View
  Widget BodyWidgetView() {
    return ScrollConfiguration(
      behavior: MyBehavior(),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Divider(
              thickness: 3,
              color: black.withOpacity(0.02),
            ),
            Padding(
              padding:
                  EdgeInsets.only(top: 20, right: 20, left: 20, bottom: 15),
              child: Container(
                height: 180,
                width: Get.width,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(15),
                  image: DecorationImage(
                    image: AssetImage(Images.foodTrendsImage),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: CommonTextWidget.PoppinsRegular(
                text:
                    "So you might remember that last year I mentioned that the hubs and I were going to ",
                color: grey5C5,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 5),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: CommonTextWidget.PoppinsRegular(
                text:
                    "South Africa, both to visit his family and for me to see the country. Well, we were supposed ",
                color: grey5C5,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 5),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: CommonTextWidget.PoppinsRegular(
                text:
                    " to go over Thanksgiving and into December, but ended up having to postpone the trip because of a delay with getting the hub's citizenship",
                color: grey5C5,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 10),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: CommonTextWidget.PoppinsRegular(
                text:
                    "So you might remember that last year I mentioned that the hubs and I were going to ",
                color: grey5C5,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 5),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: CommonTextWidget.PoppinsRegular(
                text:
                    "South Africa, both to visit his family and for me to see the country. Well, we were supposed ",
                color: grey5C5,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 5),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: CommonTextWidget.PoppinsRegular(
                text:
                    " to go over Thanksgiving and into December, but ended up having to postpone the trip because of a delay with getting the hub's citizenship",
                color: grey5C5,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: CommonTextWidget.PoppinsBold(
                text: "CHOCOLATE CAKE INGREDIENTS",
                fontSize: 14,
                color: black484,
              ),
            ),
            SizedBox(height: 20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: CommonTextWidget.PoppinsRegular(
                text: "2 cups (260g) flour",
                color: grey5C5,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: CommonTextWidget.PoppinsRegular(
                text: "2 cups (414g) sugar",
                color: grey5C5,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: CommonTextWidget.PoppinsRegular(
                text: "3/4 cup (85g) natural unsweetened cocoa "
                    "powder OR Hershey’s Special Dark Cocoa "
                    "powder",
                color: grey5C5,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: CommonTextWidget.PoppinsRegular(
                text: "2 tsp baking soda",
                color: grey5C5,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: CommonTextWidget.PoppinsRegular(
                text: "1 tsp salt",
                color: grey5C5,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: CommonTextWidget.PoppinsRegular(
                text: "2 large eggs",
                color: grey5C5,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: CommonTextWidget.PoppinsRegular(
                text: "1 cup (240ml) buttermilk",
                color: grey5C5,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: CommonTextWidget.PoppinsRegular(
                text: "1 cup (240ml) vegetable oil",
                color: grey5C5,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: CommonTextWidget.PoppinsRegular(
                text: "1 1/2 tsp vanilla extract",
                color: grey5C5,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 20),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: CommonTextWidget.PoppinsRegular(
                text: "1 cup (240ml) boiling water",
                color: grey5C5,
                fontSize: 14,
              ),
            ),
            SizedBox(height: 20),
          ],
        ),
      ),
    );
  }
}
