import 'package:flutter/material.dart';
import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

class MenuScreen extends StatelessWidget {
  final List<MenuItem> menuItems = [
    MenuItem(
      image: 'Assets/Images/pizza.png',
      name: 'Burger',
      features: ['Peynir', 'Marul', 'Domates'],
      price: 5.99,
    ),
    MenuItem(
      image: 'Assets/Images/pizza.png',
      name: 'Pizza',
      features: ['Sucuk', 'Peynir', 'Zeytin'],
      price: 8.99,
    ),
    MenuItem(
      image: 'Assets/Images/pizza.png',
      name: '<PERSON><PERSON>',
      features: ['Mar<PERSON>', 'Domates', 'Salatalık'],
      price: 4.99,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: white,
        elevation: 0,
        title: CommonTextWidget.PoppinsMedium(
          text: "Menü",
          fontSize: 24,
          color: black2D2,
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.white, Colors.grey[200]!],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: ListView.builder(
            itemCount: menuItems.length,
            itemBuilder: (context, index) {
              final item = menuItems[index];
              return Card(
                margin: EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                elevation: 5,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      GestureDetector(
                        onTap: () {
                          _openGallery(context, index);
                        },
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(15),
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: yellowF9B, width: 2),
                              borderRadius: BorderRadius.circular(15),
                            ),
                            child: Image.asset(
                              item.image,
                              height: 80,
                              width: MediaQuery.of(context).size.width * 0.3,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 20),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CommonTextWidget.PoppinsMedium(
                              text: item.name,
                              fontSize: 20,
                              color: black2D2,
                            ),
                            SizedBox(height: 5),
                            Text(
                              item.features.join(', '),
                              style: TextStyle(
                                fontSize: 14,
                                color: greyA3A,
                              ),
                            ),
                            SizedBox(height: 10),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "₺${item.price.toStringAsFixed(2)}",
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: yellowF9B,
                                  ),
                                ),
                                IconButton(
                                  icon: Icon(Icons.add_circle, color: yellowF9B, size: 32),
                                  onPressed: () {
                                    _showFeatureSelectionDialog(context, item);
                                  },
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  void _openGallery(BuildContext context, int initialIndex) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => GallerySlider(
          menuItems: menuItems,
          initialIndex: initialIndex,
        ),
      ),
    );
  }

  void _showFeatureSelectionDialog(BuildContext context, MenuItem item) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        List<String> selectedFeatures = [];
        return AlertDialog(
          title: Text("${item.name} için Özellikleri Seç"),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: item.features.map((feature) {
              return CheckboxListTile(
                title: Text(feature),
                value: selectedFeatures.contains(feature),
                onChanged: (bool? value) {
                  if (value == true) {
                    selectedFeatures.add(feature);
                  } else {
                    selectedFeatures.remove(feature);
                  }
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: Text("İptal"),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                // TODO: Add selected item with features to the cart
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: yellowF9B,
              ),
              child: Text("Sepete Ekle"),
            ),
          ],
        );
      },
    );
  }
}

class MenuItem {
  final String image;
  final String name;
  final List<String> features;
  final double price;

  MenuItem({
    required this.image,
    required this.name,
    required this.features,
    required this.price,
  });
}

class GallerySlider extends StatelessWidget {
  final List<MenuItem> menuItems;
  final int initialIndex;

  GallerySlider({required this.menuItems, required this.initialIndex});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: white,
        elevation: 0,
        title: CommonTextWidget.PoppinsMedium(
          text: "Galeri",
          fontSize: 24,
          color: black2D2,
        ),
      ),
      body: PhotoViewGallery.builder(
        itemCount: menuItems.length,
        builder: (context, index) {
          return PhotoViewGalleryPageOptions(
            imageProvider: AssetImage(menuItems[index].image),
            minScale: PhotoViewComputedScale.contained,
            maxScale: PhotoViewComputedScale.covered * 2,
          );
        },
        scrollPhysics: BouncingScrollPhysics(),
        backgroundDecoration: BoxDecoration(
          color: Colors.black,
        ),
        pageController: PageController(initialPage: initialIndex),
      ),
    );
  }
}