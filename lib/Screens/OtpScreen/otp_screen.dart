import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/font_family.dart';
import 'package:fastyorderapp/Screens/NavigationSCreen/navigation_screen.dart';
import 'package:fastyorderapp/Utills/common_button_widget.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pinput/pinput.dart';
import 'package:firebase_auth/firebase_auth.dart';

class OtpScreen extends StatelessWidget {
  final String verificationId;
  OtpScreen({Key? key, required this.verificationId}) : super(key: key);

  final TextEditingController _pinputController = TextEditingController();
  final FocusNode _pinputFocusNode = FocusNode();

  BoxDecoration get _pinputDecoration {
    return BoxDecoration(
      shape: BoxShape.circle,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        backgroundColor: white,
        automaticallyImplyLeading: false,
        elevation: 0,
        leading: Padding(
          padding: EdgeInsets.only(left: 20),
          child: InkWell(
            onTap: () {
              Get.back();
            },
            child: Icon(
              Icons.arrow_back,
              color: black2D2,
              size: 24,
            ),
          ),
        ),
      ),
      /// Body Widget View
      body: BodyWidgetView(context),
    );
  }

  /// Body Widget View
  Widget BodyWidgetView(BuildContext context) {
    return Center(
      child: Column(
        children: [
          SizedBox(height: 50),
          CommonTextWidget.PoppinsMedium(
            text: "SMS Kodu",
            fontSize: 22,
            color: black2D2,
          ),
          SizedBox(height: 10),
          CommonTextWidget.PoppinsRegular(
            text: "Cihazınıza doğrulama kodunu gönderdik",
            fontSize: 12,
            color: grey8A8,
          ),
          SizedBox(height: 35),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 25),
            child: Container(
              color: white,
              child: Pinput(
                length: 6,
                controller: _pinputController,
                focusNode: _pinputFocusNode,
                submittedPinTheme: PinTheme(
                  width: 55,
                  height: 55,
                  decoration: BoxDecoration(
                    color: white,
                    border: Border.all(color: yellowF9B),
                    shape: BoxShape.circle,
                  ),
                  textStyle: TextStyle(
                    fontFamily: FontFamily.PoppinsBold,
                    fontSize: 24,
                    color: black263,
                  ),
                ),
                focusedPinTheme: PinTheme(
                  width: 55,
                  height: 55,
                  decoration: BoxDecoration(
                    color: white,
                    border: Border.all(color: yellowF9B),
                    shape: BoxShape.circle,
                  ),
                ),
                followingPinTheme: PinTheme(
                  width: 55,
                  height: 55,
                  decoration: BoxDecoration(
                    color: whiteF2F,
                    shape: BoxShape.circle,
                  ),
                ),
                onCompleted: (String pin) async {
                  if (verificationId.isNotEmpty) {
                    try {
                      PhoneAuthCredential credential = PhoneAuthProvider.credential(
                        verificationId: verificationId,
                        smsCode: pin,
                      );
                      UserCredential userCredential = await FirebaseAuth.instance.signInWithCredential(credential);

                      if (userCredential.additionalUserInfo?.isNewUser ?? false) {
                        // New user, save user data to Firestore
                        await FirebaseFirestore.instance.collection('users').doc(userCredential.user?.uid).set({
                          'phoneNumber': userCredential.user?.phoneNumber,
                          'role': 'user',
                          'createdAt': FieldValue.serverTimestamp(),
                        });
                      }

                      Get.to(() => NavigationScreen());
                    } catch (e) {
                      print('Invalid OTP: $e');
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return AlertDialog(
                            title: Text("Error"),
                            content: Text("Invalid OTP. Please try again."),
                            actions: [
                              TextButton(
                                child: Text("OK"),
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                              ),
                            ],
                          );
                        },
                      );
                    }
                  } else {
                    print('Verification ID is empty');
                  }
                },
              ),
            ),
          ),
          Spacer(),
          Padding(
            padding: EdgeInsets.only(left: 25, right: 25, bottom: 60),
            child: CommonButtonWidget.button(
              text: "Gönder",
              onTap: () async {
                if (verificationId.isNotEmpty) {
                  try {
                    PhoneAuthCredential credential = PhoneAuthProvider.credential(
                      verificationId: verificationId,
                      smsCode: _pinputController.text,
                    );
                    UserCredential userCredential = await FirebaseAuth.instance.signInWithCredential(credential);

                    if (userCredential.additionalUserInfo?.isNewUser ?? false) {
                      // New user, save user data to Firestore
                      await FirebaseFirestore.instance.collection('users').doc(userCredential.user?.uid).set({
                        'phoneNumber': userCredential.user?.phoneNumber,
                        'role': 'user',
                        'createdAt': FieldValue.serverTimestamp(),
                      });
                    }

                    Get.to(() => NavigationScreen());
                  } catch (e) {
                    print('Invalid OTP: $e');
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          title: Text("Error"),
                          content: Text("Invalid OTP. Please try again."),
                          actions: [
                            TextButton(
                              child: Text("OK"),
                              onPressed: () {
                                Navigator.of(context).pop();
                              },
                            ),
                          ],
                        );
                      },
                    );
                  }
                } else {
                  print('Verification ID is empty');
                }
              },
              buttonColor: yellowF9B,
            ),
          ),
        ],
      ),
    );
  }
}