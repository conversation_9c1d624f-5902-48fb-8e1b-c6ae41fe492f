import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Controllers/filter_controller.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/main.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class FilterScreen1 extends StatefulWidget {
  const FilterScreen1({Key? key}) : super(key: key);

  @override
  State<FilterScreen1> createState() => _FilterScreen1State();
}

class _FilterScreen1State extends State<FilterScreen1> {
  int selectedIndex = 0;

  final FilterController filterController = Get.put(FilterController());

  final List<Map> dealsList = [
    {
      "text": "Prepaid Deals",
    },
    {
      "text": "15% Discount",
    },
    {
      "text": "35% Discount",
    },
    {
      "text": "25% Discount",
    },
    {
      "text": "20% Discount",
    },
    {
      "text": "10% Discount",
    },
    {
      "text": "Mindblowing 50% Discount",
    },
    {
      "text": "Free Beverages",
    },
    {
      "text": "Free Cocktail or Mocktail",
    },
    {
      "text": "30% Discount",
    },
    {
      "text": "Free Dessert",
    },
    {
      "text": "Happy Hours",
    },
    {
      "text": "Free Glass of Wine",
    },
    {
      "text": "Free Appetizer",
    },
  ];

  final List<Map> categoryList = [
    {
      "text": "Casual Dining",
    },
    {
      "text": "Luxury Dining",
    },
    {
      "text": "Hotel Dining",
    },
    {
      "text": "Bar/Pub",
    },
    {
      "text": "Sweet Shop",
    },
    {
      "text": "Cafe/Bakery",
    },
    {
      "text": "Delivery Only",
    },
    {
      "text": "Takeaway",
    },
    {
      "text": "Other",
    },
  ];

  final List<Map> typeOfMealList = [
    {
      "text": "Set Menu",
    },
    {
      "text": "A’la carte",
    },
    {
      "text": "Buffet",
    },
  ];

  final List<Map> otherDealTypeList = [
    {
      "text": "Prepaid deals",
    },
    {
      "text": "Prime deals",
    },
    {
      "text": "PayEazy",
    },
    {
      "text": "Super Saver Deals",
    },
  ];
  final List<Map> cuisinesList = [
    {
      "text": "North Indian",
    },
    {
      "text": "Chinese",
    },
    {
      "text": "Modern Indian",
    },
    {
      "text": "Italian",
    },
    {
      "text": "Cafe",
    },
    {
      "text": "Pan Asian",
    },
    {
      "text": "South Indian",
    },
    {
      "text": "Fast Food",
    },
    {
      "text": "Finger Food",
    },
    {
      "text": "Fusion",
    },
    {
      "text": "Indian",
    },
    {
      "text": "Muqhlai",
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 145),
      child: Container(
        height: Get.height,
        width: Get.width,
        color: white,
        child: Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 25, vertical: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CommonTextWidget.PoppinsMedium(
                    color: black2D2,
                    fontSize: 18,
                    text: "Filters",
                  ),
                  InkWell(
                    onTap: (){
                      Get.back();
                    },
                    child: Icon(Icons.close, color: black2D2, size: 20),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 12),
              child: Divider(thickness: 1, color: greyD4D),
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: Get.height,
                  decoration: BoxDecoration(
                    boxShadow: [
                      BoxShadow(
                        color: black.withOpacity(0.1),
                        offset: Offset(4, 4),
                        spreadRadius: 0,
                        blurRadius: 16,
                      ),
                    ],
                    color: white,
                  ),
                  child: NavigationRail(
                    selectedIndex: selectedIndex,
                    onDestinationSelected: (int index) {
                      setState(() {
                        selectedIndex = index;
                      });
                    },
                    labelType: NavigationRailLabelType.all,
                    elevation: 1,
                    destinations: [
                      NavigationRailDestination(
                        icon: Container(),
                        label: CommonTextWidget.PoppinsMedium(
                          color: selectedIndex == 0 ? yellowF9B : black2D2,
                          fontSize: 12,
                          text: "Deals",
                        ),
                      ),
                      NavigationRailDestination(
                        icon: Container(),
                        label: CommonTextWidget.PoppinsMedium(
                          color: selectedIndex == 1 ? yellowF9B : black2D2,
                          fontSize: 12,
                          text: "Category",
                        ),
                      ),
                      NavigationRailDestination(
                        icon: Container(),
                        label: CommonTextWidget.PoppinsMedium(
                          color: selectedIndex == 2 ? yellowF9B : black2D2,
                          fontSize: 12,
                          text: "Type of Meal",
                        ),
                      ),
                      NavigationRailDestination(
                        icon: Container(),
                        label: CommonTextWidget.PoppinsMedium(
                          color: selectedIndex == 3 ? yellowF9B : black2D2,
                          fontSize: 12,
                          text: "Other Deal Types",
                        ),
                      ),
                      NavigationRailDestination(
                        icon: Container(),
                        label: CommonTextWidget.PoppinsMedium(
                          color: selectedIndex == 4 ? yellowF9B : black2D2,
                          fontSize: 12,
                          text: "Cuisines",
                        ),
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: ScrollConfiguration(
                    behavior: MyBehavior(),
                    child: selectedIndex == 0
                        ? ListView.builder(
                            padding:
                                EdgeInsets.only(top: 30, left: 20, right: 20),
                            shrinkWrap: true,
                            itemCount: dealsList.length,
                            itemBuilder: (context, index) => Padding(
                              padding: EdgeInsets.only(bottom: 30),
                              child: Row(
                                children: [
                                  GetBuilder<FilterController>(
                                    init: FilterController(),
                                    builder: (controller) => InkWell(
                                      onTap: () {
                                        filterController.onIndexChange1(index);
                                      },
                                      child: Container(
                                        height: 20,
                                        width: 20,
                                        decoration: BoxDecoration(
                                          color:
                                              filterController.selectedIndex1 ==
                                                      index
                                                  ? yellowF9B
                                                  : white,
                                          borderRadius:
                                              BorderRadius.circular(5),
                                          border: Border.all(
                                              color: filterController
                                                          .selectedIndex1 ==
                                                      index
                                                  ? yellowF9B
                                                  : greyA0A),
                                        ),
                                        child: Center(
                                          child: Icon(
                                            Icons.check,
                                            color: white,
                                            size: 16,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  SizedBox(width: 10),
                                  CommonTextWidget.PoppinsRegular(
                                      text: dealsList[index]["text"],
                                      color: black2D2,
                                      fontSize: 12),
                                ],
                              ),
                            ),
                          )
                        : selectedIndex == 1
                            ? ListView.builder(
                                padding: EdgeInsets.only(
                                    top: 30, left: 20, right: 20, bottom: 80),
                                shrinkWrap: true,
                                itemCount: categoryList.length,
                                itemBuilder: (context, index) => Padding(
                                  padding: EdgeInsets.only(bottom: 30),
                                  child: Row(
                                    children: [
                                      GetBuilder<FilterController>(
                                        init: FilterController(),
                                        builder: (controller1) => InkWell(
                                          onTap: () {
                                            filterController
                                                .onIndexChange2(index);
                                          },
                                          child: Container(
                                            height: 20,
                                            width: 20,
                                            decoration: BoxDecoration(
                                              color: filterController
                                                          .selectedIndex2 ==
                                                      index
                                                  ? yellowF9B
                                                  : white,
                                              borderRadius:
                                                  BorderRadius.circular(5),
                                              border: Border.all(
                                                  color: filterController
                                                              .selectedIndex2 ==
                                                          index
                                                      ? yellowF9B
                                                      : greyA0A),
                                            ),
                                            child: Center(
                                              child: Icon(
                                                Icons.check,
                                                color: white,
                                                size: 16,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      SizedBox(width: 10),
                                      CommonTextWidget.PoppinsRegular(
                                          text: categoryList[index]["text"],
                                          color: black2D2,
                                          fontSize: 12),
                                    ],
                                  ),
                                ),
                              )
                            : selectedIndex == 2
                                ? ListView.builder(
                                    padding: EdgeInsets.only(
                                        top: 30,
                                        left: 20,
                                        right: 20,
                                        bottom: 80),
                                    shrinkWrap: true,
                                    itemCount: typeOfMealList.length,
                                    itemBuilder: (context, index) => Padding(
                                      padding: EdgeInsets.only(bottom: 30),
                                      child: Row(
                                        children: [
                                          GetBuilder<FilterController>(
                                            init: FilterController(),
                                            builder: (controller2) => InkWell(
                                              onTap: () {
                                                filterController
                                                    .onIndexChange3(index);
                                              },
                                              child: Container(
                                                height: 20,
                                                width: 20,
                                                decoration: BoxDecoration(
                                                  color: filterController
                                                              .selectedIndex3 ==
                                                          index
                                                      ? yellowF9B
                                                      : white,
                                                  borderRadius:
                                                      BorderRadius.circular(5),
                                                  border: Border.all(
                                                      color: filterController
                                                                  .selectedIndex3 ==
                                                              index
                                                          ? yellowF9B
                                                          : greyA0A),
                                                ),
                                                child: Center(
                                                  child: Icon(
                                                    Icons.check,
                                                    color: white,
                                                    size: 16,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          SizedBox(width: 10),
                                          CommonTextWidget.PoppinsRegular(
                                              text: typeOfMealList[index]
                                                  ["text"],
                                              color: black2D2,
                                              fontSize: 12),
                                        ],
                                      ),
                                    ),
                                  )
                                : selectedIndex == 3
                                    ? ListView.builder(
                                        padding: EdgeInsets.only(
                                            top: 30,
                                            left: 20,
                                            right: 20,
                                            bottom: 80),
                                        shrinkWrap: true,
                                        itemCount: otherDealTypeList.length,
                                        itemBuilder: (context, index) =>
                                            Padding(
                                          padding: EdgeInsets.only(bottom: 30),
                                          child: Row(
                                            children: [
                                              GetBuilder<FilterController>(
                                                init: FilterController(),
                                                builder: (controller2) =>
                                                    InkWell(
                                                  onTap: () {
                                                    filterController
                                                        .onIndexChange4(index);
                                                  },
                                                  child: Container(
                                                    height: 20,
                                                    width: 20,
                                                    decoration: BoxDecoration(
                                                      color: filterController
                                                                  .selectedIndex4 ==
                                                              index
                                                          ? yellowF9B
                                                          : white,
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              5),
                                                      border: Border.all(
                                                          color: filterController
                                                                      .selectedIndex4 ==
                                                                  index
                                                              ? yellowF9B
                                                              : greyA0A),
                                                    ),
                                                    child: Center(
                                                      child: Icon(
                                                        Icons.check,
                                                        color: white,
                                                        size: 16,
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              SizedBox(width: 10),
                                              CommonTextWidget.PoppinsRegular(
                                                  text: otherDealTypeList[index]
                                                      ["text"],
                                                  color: black2D2,
                                                  fontSize: 12),
                                            ],
                                          ),
                                        ),
                                      )
                                    : selectedIndex == 4
                                        ? ListView.builder(
                                            padding: EdgeInsets.only(
                                                top: 30,
                                                left: 20,
                                                right: 20,
                                                bottom: 80),
                                            shrinkWrap: true,
                                            itemCount: cuisinesList.length,
                                            itemBuilder: (context, index) =>
                                                Padding(
                                              padding:
                                                  EdgeInsets.only(bottom: 30),
                                              child: Row(
                                                children: [
                                                  GetBuilder<FilterController>(
                                                    init: FilterController(),
                                                    builder: (controller2) =>
                                                        InkWell(
                                                      onTap: () {
                                                        filterController
                                                            .onIndexChange5(
                                                                index);
                                                      },
                                                      child: Container(
                                                        height: 20,
                                                        width: 20,
                                                        decoration:
                                                            BoxDecoration(
                                                          color: filterController
                                                                      .selectedIndex5 ==
                                                                  index
                                                              ? yellowF9B
                                                              : white,
                                                          borderRadius:
                                                              BorderRadius
                                                                  .circular(5),
                                                          border: Border.all(
                                                              color: filterController
                                                                          .selectedIndex5 ==
                                                                      index
                                                                  ? yellowF9B
                                                                  : greyA0A),
                                                        ),
                                                        child: Center(
                                                          child: Icon(
                                                            Icons.check,
                                                            color: white,
                                                            size: 16,
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(width: 10),
                                                  CommonTextWidget
                                                      .PoppinsRegular(
                                                          text: cuisinesList[
                                                              index]["text"],
                                                          color: black2D2,
                                                          fontSize: 12),
                                                ],
                                              ),
                                            ),
                                          )
                                        : Container(),
                  ),
                ),
              ],
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 25, vertical: 20),
              child: Row(
                children: [
                  CommonTextWidget.PoppinsMedium(
                    color: black2D2,
                    fontSize: 18,
                    text: "Filters",
                  ),
                  InkWell(
                    onTap: (){
                      Get.back();
                    },
                    child: Icon(Icons.close, color: black2D2, size: 20),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 12),
              child: Divider(thickness: 1, color: greyD4D),
            ),
            Container(
              height: 90,
              width: Get.width,
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: black.withOpacity(0.1),
                    offset: Offset(0, -4),
                    spreadRadius: 0,
                    blurRadius: 10,
                  ),
                ],
                color: white,
              ),
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 25),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InkWell(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        height: 38,
                        width: 95,
                        decoration: BoxDecoration(
                          color: black2D2,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Center(
                          child: CommonTextWidget.PoppinsSemiBold(
                            text: "Clear",
                            color: white,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        Get.back();
                      },
                      child: Container(
                        height: 38,
                        width: 95,
                        decoration: BoxDecoration(
                          color: yellowF9B,
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Center(
                          child: CommonTextWidget.PoppinsSemiBold(
                            text: "Apply",
                            color: white,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
