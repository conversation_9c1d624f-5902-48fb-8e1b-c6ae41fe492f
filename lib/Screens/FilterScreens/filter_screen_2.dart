import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Controllers/filter_controller.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/main.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class FilterScreen2 extends StatelessWidget {
  FilterScreen2({Key? key}) : super(key: key);

  final List sortByFilterList = [
    "Popularity",
    "Distance: Nearest first",
    "Rating: High to Low",
    "Price: High to Low",
    "Price: Low to High",
  ];

  final FilterController filterController = Get.put(FilterController());

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 300),
      child: Container(
        height: Get.height,
        width: Get.width,
        color: white,
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 25, vertical: 20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CommonTextWidget.PoppinsMedium(
                    color: black2D2,
                    fontSize: 18,
                    text: "Sort By",
                  ),
                  InkWell(
                    onTap: () {
                      Get.back();
                    },
                    child: Icon(Icons.close, color: black2D2, size: 20),
                  ),
                ],
              ),
            ),
            ScrollConfiguration(
              behavior: MyBehavior(),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: sortByFilterList.length,
                itemBuilder: (context, index) => GetBuilder<FilterController>(
                  init: FilterController(),
                  builder: (controller) => InkWell(
                    onTap: () {
                      filterController.onIndexChange(index);
                    },
                    child: Container(
                      padding: EdgeInsets.only(left: 8, top: 14, bottom: 14),
                      decoration: BoxDecoration(
                        color: filterController.selectedIndex == index
                            ? yellowFFF1
                            : white,
                      ),
                      child: filterController.selectedIndex == index
                          ? CommonTextWidget.PoppinsSemiBold(
                              text: sortByFilterList[index],
                              color: black2D2,
                              fontSize: 14,
                            )
                          : CommonTextWidget.PoppinsMedium(
                              text: sortByFilterList[index],
                              color: black2D2,
                              fontSize: 14,
                            ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
