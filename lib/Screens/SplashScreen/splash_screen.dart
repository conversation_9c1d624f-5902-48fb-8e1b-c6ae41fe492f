import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Controllers/splash_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class SplashScreen extends StatelessWidget {
  SplashScreen({Key? key}) : super(key: key);

  final SplashController splashController = Get.put(SplashController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      body: Stack(
        alignment: Alignment.bottomCenter,
        children: [
          Center(
            child: SvgPicture.asset(Images.splashImage),
          ),
          Image.asset(Images.splashBackGroundImage),
        ],
      ),
    );
  }
}
