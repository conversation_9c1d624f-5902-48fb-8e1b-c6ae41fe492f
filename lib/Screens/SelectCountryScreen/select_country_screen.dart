import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Controllers/select_country_controller.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:fastyorderapp/main.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SelectCountryScreen extends StatelessWidget {
  SelectCountryScreen({Key? key}) : super(key: key);

  final List countryList = [
    {"image": Images.argentina, "country": "Argentina"},
    {"image": Images.australia, "country": "Australia"},
    {"image": Images.belgique, "country": "Belgique"},
    {"image": Images.brazil, "country": "Brazil"},
    {"image": Images.canadaEnglish, "country": "Canada (English)"},
    {"image": Images.canadaEnglish, "country": "Canada (Francais)"},
    {"image": Images.chile, "country": "Chile"},
    {"image": Images.colombia, "country": "Colombia"},
    {"image": Images.cesko, "country": "Cesko"},
    {"image": Images.danmark, "country": "Danmark"},
    {"image": Images.deutschland, "country": "Deutschland"},
    {"image": Images.espana, "country": "Espana"},
    {"image": Images.france, "country": "France"},
    {"image": Images.india, "country": "India"},
    {"image": Images.ireland, "country": "Ireland"},
    {"image": Images.israel, "country": "Israel"},
    {"image": Images.italia, "country": "Italia"},
    {"image": Images.malaysia, "country": "Malaysia"},
    {"image": Images.maxico, "country": "Maxico"},
    {"image": Images.nederland, "country": "Nederland"},
    {"image": Images.newZealand, "country": "New Zealand"},
    {"image": Images.osterreich, "country": "Osterreich"},
    {"image": Images.peru, "country": "Peru"},
    {"image": Images.philippines, "country": "Philippines"},
    {"image": Images.polska, "country": "Polska"},
    {"image": Images.portugal, "country": "Portugal"},
    {"image": Images.schweiz, "country": "Schweiz (Deutsch)"},
    {"image": Images.singapore, "country": "Singapore"},
    {"image": Images.slovensko, "country": "Slovensko"},
    {"image": Images.suisse, "country": "Suisse (Francais)"},
    {"image": Images.suomi, "country": "Suomi"},
    {"image": Images.sverige, "country": "Sverige"},
    {"image": Images.suisse, "country": "Svizzera (italiano)"},
    {"image": Images.suisse, "country": "Switzerland (English)"},
    {"image": Images.thailand, "country": "Thailand (English)"},
    {"image": Images.turkiye, "country": "Turkiye"},
    {"image": Images.unitedArabEmirates, "country": "United Arab Emirates"},
    {"image": Images.UK, "country": "United Kingdom"},
    {"image": Images.US, "country": "United States"},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        backgroundColor: white,
        automaticallyImplyLeading: false,
        centerTitle: true,
        elevation: 0,
        leading: Padding(
          padding:  EdgeInsets.only(left: 20),
          child: InkWell(
            onTap: () {
              Get.back();
            },
            child: Icon(
              Icons.arrow_back,
              color: black2D2,
              size: 24,
            ),
          ),
        ),
        title: CommonTextWidget.PoppinsMedium(
          text: "Select Country",
          fontSize: 18,
          color: black2D2,
        ),
      ),
      body: CountryListWidgetView(),
    );
  }

  /// CountryList Widget View
  Widget CountryListWidgetView() {
    return ScrollConfiguration(
      behavior: MyBehavior(),
      child: GetBuilder<DefaultThemeSelectLanguageController>(
        init: DefaultThemeSelectLanguageController(),
        builder: (controller) => ListView.builder(
          padding: EdgeInsets.only(bottom: 20),
          itemCount: countryList.length,
          shrinkWrap: true,
          itemBuilder: (context, index) => Column(
            children: [
              ListTile(
                contentPadding: EdgeInsets.symmetric(horizontal: 25),
                onTap: () {
                  controller.onIndexChange(index);
                },
                leading: Image.asset(countryList[index]["image"],
                    height: 29, width: 29),
                title: CommonTextWidget.PoppinsRegular(
                  text: countryList[index]["country"],
                  fontSize: 14,
                  color: black2D2,
                ),
                trailing: Container(
                  height: 18,
                  width: 18,
                  decoration: BoxDecoration(
                      color: white,
                      shape: BoxShape.circle,
                      border: Border.all(color: black2D2)),
                  alignment: Alignment.center,
                  child: controller.selectedIndex == index
                      ? Container(
                          height: 10,
                          width: 10,
                          decoration: BoxDecoration(
                              color: black2D2, shape: BoxShape.circle),
                        )
                      : SizedBox.shrink(),
                ),
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 12),
                child: Divider(
                  color: greyBFB,
                  thickness: 1,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
