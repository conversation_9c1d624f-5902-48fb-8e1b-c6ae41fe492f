import 'package:fastyorderapp/Constants/colors.dart';
import 'package:fastyorderapp/Constants/images.dart';
import 'package:fastyorderapp/Screens/EnterMobileNumberScreen/enter_mobile_number_screen.dart';
import 'package:fastyorderapp/Utills/common_button_widget.dart';
import 'package:fastyorderapp/Utills/common_text_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({Key? key}) : super(key: key);

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> {
  PageController pageController = PageController(initialPage: 0);
  int index = 0;
  List pageViewList = [
    {
      "image": Images.welcomeImage1,
      "text1": "<PERSON><PERSON><PERSON><PERSON><PERSON> özel indirimlere rezerve edin",
      "text2":
          "Masanızı anında güvenceye alın ve %50'ye varan indirimlerden yararlanın."
    },
    {
      "image": Images.welcomeImage2,
      "text1": "Uygulamadan ödeyin, ekstra indirimler kazanın",
      "text2":
          "Farklı ödeme avantajlarından yararlanın ve ekstra indirimler kazanın."
    },
    {
      "image": Images.welcomeImage3,
      "text1": "Gel al siparişlerde %50'ye varan indirimler",
      "text2": "Sıra beklemeyin, gel al siparişlerde %50'ye varan indirimler"
    },
    {
      "image": Images.welcomeImage4,
      "text1": "Ücretsiz yemek kuponları için FastyPoints kazanın ve kullanın",
      "text2":
          "FastyPoints kazanarak ücretsiz yemek kuponları kazanın ve kullanın."
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      body: Stack(
        children: [
          /// Top Widget View
          TopWidgetView(),

          /// PageView Widget View
          PageViewWidgetView(),

          /// Bottom Widget View
          BottomWidgetView(),
        ],
      ),
    );
  }

  /// Top Widget View
  Widget TopWidgetView() {
    return Align(
      alignment: Alignment.topCenter,
      child: Padding(
        padding: EdgeInsets.only(top: 50),
        child: Image.asset(Images.dinnerTableTextImage, height: 35, width: 139),
      ),
    );
  }

  /// PageView Widget View
  Widget PageViewWidgetView() {
    return PageView.builder(
      physics: BouncingScrollPhysics(),
      itemCount: pageViewList.length,
      controller: pageController,
      scrollDirection: Axis.horizontal,
      onPageChanged: (i) {
        setState(
          () {
            index = i;
          },
        );
      },
      itemBuilder: (context, index) {
        return Padding(
          padding: EdgeInsets.only(left: 15, top: 150, right: 15, bottom: 140),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SvgPicture.asset(
                pageViewList[index]["image"],
                fit: BoxFit.cover,
              ),
              SizedBox(height: 30),
              Padding(
                padding: EdgeInsets.only(left: 25, right: 25),
                child: CommonTextWidget.PoppinsMedium(
                    text: pageViewList[index]["text1"],
                    color: black2D2,
                    fontSize: 22,
                    textAlign: TextAlign.center),
              ),
              SizedBox(height: 20),
              Padding(
                padding: EdgeInsets.only(left: 25, right: 25),
                child: CommonTextWidget.PoppinsRegular(
                    text: pageViewList[index]["text2"],
                    color: grey8A8,
                    fontSize: 12,
                    textAlign: TextAlign.center),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Bottom Widget View
  Widget BottomWidgetView() {
    return Padding(
      padding: EdgeInsets.only(bottom: 50),
      child: Align(
        alignment: Alignment.bottomCenter,
        child: index == 3
            ? Padding(
                padding: EdgeInsets.symmetric(horizontal: 85),
                child: CommonButtonWidget.button(
                  onTap: () {
                    Get.to(() => EnterMobileNumberScreen());
                  },
                  text: "Fasty Dünyası",
                  buttonColor: yellowF9B,
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  pageViewList.length,
                  (position) => Padding(
                    padding: EdgeInsets.only(right: 8),
                    child: Container(
                      width: index == position ? 30 : 10,
                      height: 4,
                      decoration: BoxDecoration(
                        color: index == position ? yellowF9B : greyCCC,
                      ),
                    ),
                  ),
                ),
              ),
      ),
    );
  }
}
