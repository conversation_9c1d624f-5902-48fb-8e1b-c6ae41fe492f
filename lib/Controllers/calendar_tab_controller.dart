import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CalendarTabController extends GetxController with GetSingleTickerProviderStateMixin{
  final List<Tab> myTabs = <Tab>[
    Tab(text: '<PERSON>zer<PERSON>yon<PERSON>'),
    Tab(text: '<PERSON><PERSON><PERSON><PERSON>'),
  ];

  late TabController controller;

  @override
  void onInit() {
    super.onInit();
    controller = TabController(vsync: this, length: myTabs.length);
  }

  @override
  void onClose() {
    controller.dispose();
    super.onClose();
  }
}