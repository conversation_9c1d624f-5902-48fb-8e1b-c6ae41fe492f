import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CafeListTabController extends GetxController with GetSingleTickerProviderStateMixin{
  final List<Tab> myTabs = <Tab>[
Tab(text: 'Steak'),
Tab(text: '<PERSON><PERSON><PERSON><PERSON><PERSON>'),
<PERSON>b(text: '<PERSON><PERSON><PERSON><PERSON>'),
<PERSON>b(text: '<PERSON><PERSON><PERSON><PERSON>'),
<PERSON>b(text: '<PERSON><PERSON>'),
  ];

  late TabController controller;

  @override
  void onInit() {
    super.onInit();
    controller = TabController(vsync: this, length: myTabs.length);
  }

  @override
  void onClose() {
    controller.dispose();
    super.onClose();
  }
}