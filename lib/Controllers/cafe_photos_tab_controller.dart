import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CafePhotosTabController extends GetxController with GetSingleTickerProviderStateMixin{
  final List<Tab> myTabs = <Tab>[
    Tab(text: 'All Photos 31'),
    Tab(text: 'Restaurant Uploaded'),
    Tab(text: 'Guest Uploaded'),
  ];

  late TabController controller;

  @override
  void onInit() {
    super.onInit();
    controller = TabController(vsync: this, length: myTabs.length);
  }

  @override
  void onClose() {
    controller.dispose();
    super.onClose();
  }
}