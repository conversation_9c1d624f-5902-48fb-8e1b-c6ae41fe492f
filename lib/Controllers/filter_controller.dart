import 'package:get/get.dart';

class FilterController extends GetxController{
  // ignore: prefer_typing_uninitialized_variables
  var selectedIndex1;

  onIndexChange1(index) {
    selectedIndex1 = index;
    update();
  }

  // ignore: prefer_typing_uninitialized_variables
  var selectedIndex2;

  onIndexChange2(index) {
    selectedIndex2 = index;
    update();
  }

  // ignore: prefer_typing_uninitialized_variables
  var selectedIndex3;

  onIndexChange3(index) {
    selectedIndex3 = index;
    update();
  }

  // ignore: prefer_typing_uninitialized_variables
  var selectedIndex4;

  onIndexChange4(index) {
    selectedIndex4 = index;
    update();
  }

  // ignore: prefer_typing_uninitialized_variables
  var selectedIndex5;

  onIndexChange5(index) {
    selectedIndex5 = index;
    update();
  }

  // ignore: prefer_typing_uninitialized_variables
  var selectedIndex;

  onIndexChange(index) {
    selectedIndex = index;
    update();
  }
}