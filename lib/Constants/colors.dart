import 'package:flutter/material.dart';

const Color yellowF9B = Color(0xffEF5631);
const Color white = Color(0xffFFFFFF);
const Color black2D2 = Color(0xff2D2513);
const Color grey8A8 = Color(0xff8A867C);
const Color greyCCC = Color(0xffCCCCCC);
const Color grey9C9 = Color(0xff9C9C9C);
const Color whiteF2F = Color(0xffF2F2F2);
const Color greyBFB = Color(0xffBFBFBF);
const Color black263 = Color(0xff263238);
const Color grey949 = Color(0xff949A98);
const Color greyE2E = Color(0xffE2E2E2);
const Color black = Color(0xff000000);
const Color greyB0B = Color(0xffB0B0B0);
const Color yellowFFF = Color(0xffFFF6E7);
const Color grey5C5 = Color(0xff5C5C5C);
const Color black484 = Color(0xff484848);
const Color greyDCD = Color(0xffDCDCDC);
const Color grey969 = Color(0xff969696);
const Color green55A = Color(0xff55A759);
const Color greyECE = Color(0xffECECEC);
const Color grey707 = Color(0xff707070);
const Color grey8D8 = Color(0xff8D8D8D);
const Color greyE0E = Color(0xffE0E0E0);
const Color greyA3A = Color(0xffA3A3A3);
const Color greyF8F = Color(0xffF8F8F8);
const Color black616 = Color(0xff616161);
const Color greyD4D = Color(0xffD4D4D4);
const Color greyF5F = Color(0xffF5F5F5);
const Color grey545 = Color(0xff545454);
const Color redFB4 = Color(0xffFB4717);
const Color green43A = Color(0xff43A047);
const Color greyD8D = Color(0xffD8D8D8);
const Color grey7B7 = Color(0xff7B7B7B);
const Color yellowFEF = Color(0xffFEF4E1);
const Color yellowFFF1 = Color(0xffFFF1D7);
const Color grey9E9 = Color(0xff9E9E9E);
const Color blackD5D = Color(0xffD5D5D5);
const Color grey939 = Color(0xff939393);
const Color greyA0A = Color(0xffA0A0A0);
const Color green009 = Color(0xff009C08);
const Color grey868 = Color(0xff868686);
const Color grey909 = Color(0xff909090);
const Color grey9D9 = Color(0xffD9D9D9);
const Color black101 = Color(0xff101010);
const Color blue499 = Color(0xff4990E2);
const Color greyDAD = Color(0xffDADADA);
const Color grey818 = Color(0xff818181);
const Color greyE5E = Color(0xffE5E5E5);
const Color redF03 = Color(0xffF03C39);
