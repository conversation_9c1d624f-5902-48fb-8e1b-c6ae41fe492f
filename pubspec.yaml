name: fastyorderapp
description: A new Flutter project.

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+2

environment:
  sdk: ">=2.17.1 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  get: ^4.6.6
  flutter_svg: ^2.0.10+1
  pinput: ^5.0.0
  carousel_slider: ^5.0.0
  flutter_rating_bar: ^4.0.0
  dotted_border: ^2.0.0+2
  percent_indicator: ^4.2.2
  firebase_auth: ^5.3.1
  firebase_core: ^3.6.0
  cloud_firestore: ^5.4.4
  provider: ^6.1.2
  flutter_localizations:
    sdk: flutter
  image_picker: ^1.1.2
  firebase_storage: ^12.3.4
  photo_view: ^0.15.0
  geolocator: ^13.0.1
  geocoding: ^3.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true

  assets:
    - Assets/Images/
    - Assets/Images/SplashScreenImages/
    - Assets/Images/WelcomeScreenImages/
    - Assets/Images/EnterMobileNumberScreenImages/
    - Assets/Images/SelectCountryScreenImages/
    - Assets/Images/CurrentLocationScreenImages/
    - Assets/Images/SelectLocationScreenImages/
    - Assets/Images/NavigationScreenImages/
    - Assets/Images/HomeScreenImages/
    - Assets/Images/SearchScreenImages/
    - Assets/Images/FoodTrendsScreenImage/
    - Assets/Images/ScanCodeScreenImages/
    - Assets/Images/CafeListScreenImages/
    - Assets/Images/CafeDetailScreenImages/
    - Assets/Images/EnjoyYourMealScreenImages/
    - Assets/Images/PageViewScreenImage/
    - Assets/Images/CancelBookingScreenImages/
    - Assets/Images/MenuScreenImages/
    - Assets/Images/UserReviewScreenImages/
    - Assets/Images/CafePhotoScreenImages/
    - Assets/Images/CalendarScreenImages/
    - Assets/Images/ReferAndEarnScreenImages/
    - Assets/Images/PayBillScreenImages/
    - Assets/Images/MakepaymentScreenImages/
    - Assets/Images/PaymentSuccessfulScreenImages/
    - Assets/Images/PrimeMemberShipScreenImages/
    - Assets/Images/ProfileScreenImages/

  fonts:
    - family: PoppinsBold
      fonts:
        - asset: assets/fonts/Poppins-Bold.ttf
    - family: PoppinsLight
      fonts:
        - asset: assets/fonts/Poppins-Light.ttf
    - family: PoppinsMedium
      fonts:
        - asset: assets/fonts/Poppins-Medium.ttf
    - family: PoppinsRegular
      fonts:
        - asset: assets/fonts/Poppins-Regular.ttf
    - family: PoppinsSemiBold
      fonts:
        - asset: assets/fonts/Poppins-SemiBold.ttf